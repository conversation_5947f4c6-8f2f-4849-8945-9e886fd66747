# اختبار ميزة التحكم في تكرار صف التوصيل

## الوصف
تم إضافة ميزة جديدة للتحكم في عرض صف التوصيل في ملخص الطلب عند إضافة عدة منتجات للسلة.

## الملفات المُحدثة

### 1. admin/partials/tabs/tab-shipping.php
- إضافة قسم جديد "إعدادات التحكم في التوصيل"
- إضافة خيار checkbox بعنوان "إخفاء صف التوصيل المكرر"
- إضافة CSS وJavaScript للتفاعل مع الإعداد

### 2. includes/class-pexlat-form-admin.php
- إضافة معالجة للإعداد الجديد `disable_duplicate_shipping_row` في دالة save_form
- إضافة الإعداد إلى قائمة إعدادات الشحن في دالة ensure_form_settings

### 3. public/partials/form-template.php
- إضافة class="shipping-price-row" لصفوف التوصيل
- إضافة data-disable-duplicate attribute
- إضافة دالة handleShippingRowDisplay() في JavaScript
- إضافة استدعاء الدالة عند تحميل الصفحة وتحديث الكمية

### 4. public/js/pexlat-form-public.js
- إضافة دالة handleShippingRowDisplay() للتحكم في عرض صف التوصيل
- إضافة استدعاء الدالة في updateTotalPrice()

## كيفية الاختبار

### السيناريو 1: الإعداد مُعطل (الوضع الافتراضي)
1. اذهب إلى لوحة التحكم > إعدادات النموذج > طرق التوصيل
2. تأكد من أن خيار "إخفاء صف التوصيل المكرر" غير مُفعل
3. احفظ الإعدادات
4. اذهب إلى صفحة المنتج
5. أضف كمية أكبر من 1 (مثل 3 منتجات)
6. **النتيجة المتوقعة**: يجب أن يظهر صف التوصيل في ملخص الطلب كما هو عادة

### السيناريو 2: الإعداد مُفعل مع منتج واحد
1. اذهب إلى لوحة التحكم > إعدادات النموذج > طرق التوصيل
2. فعّل خيار "إخفاء صف التوصيل المكرر"
3. احفظ الإعدادات
4. اذهب إلى صفحة المنتج
5. اتركة الكمية 1
6. **النتيجة المتوقعة**: يجب أن يظهر صف التوصيل في ملخص الطلب بشكل طبيعي

### السيناريو 3: الإعداد مُفعل مع عدة منتجات
1. اذهب إلى لوحة التحكم > إعدادات النموذج > طرق التوصيل
2. تأكد من أن خيار "إخفاء صف التوصيل المكرر" مُفعل
3. احفظ الإعدادات
4. اذهب إلى صفحة المنتج
5. غيّر الكمية إلى أكثر من 1 (مثل 3 منتجات)
6. **النتيجة المتوقعة**: 
   - يجب أن يختفي صف التوصيل من ملخص الطلب
   - يجب أن تظهر ملاحظة "تكلفة التوصيل محسوبة في السعر الإجمالي"

### السيناريو 4: تغيير الكمية ديناميكياً
1. تأكد من أن الإعداد مُفعل
2. اذهب إلى صفحة المنتج
3. ابدأ بكمية 1 - يجب أن يظهر صف التوصيل
4. غيّر الكمية إلى 2 - يجب أن يختفي صف التوصيل وتظهر الملاحظة
5. أعد الكمية إلى 1 - يجب أن يظهر صف التوصيل مرة أخرى وتختفي الملاحظة

## التحقق من الوظائف

### 1. لوحة التحكم
- [ ] يظهر قسم "إعدادات التحكم في التوصيل" في تبويب طرق التوصيل
- [ ] يعمل checkbox "إخفاء صف التوصيل المكرر" بشكل صحيح
- [ ] تظهر رسالة تأكيد عند تغيير الإعداد
- [ ] يتم حفظ الإعداد بشكل صحيح

### 2. واجهة المستخدم
- [ ] يتم إخفاء صف التوصيل عند الكمية > 1 والإعداد مُفعل
- [ ] تظهر ملاحظة توضيحية عند إخفاء صف التوصيل
- [ ] يظهر صف التوصيل عند الكمية = 1 أو الإعداد مُعطل
- [ ] تعمل الوظيفة مع كلا من العرض التفصيلي والمبسط لطرق التوصيل

### 3. JavaScript
- [ ] تعمل دالة handleShippingRowDisplay() بشكل صحيح
- [ ] يتم استدعاء الدالة عند تحميل الصفحة
- [ ] يتم استدعاء الدالة عند تغيير الكمية
- [ ] تعمل الوظيفة في كلا من form-template.php و pexlat-form-public.js

## ملاحظات
- الإعداد يؤثر فقط على العرض ولا يؤثر على حساب تكلفة التوصيل الفعلية
- الوظيفة تعمل مع كلا من العرض التفصيلي والمبسط لطرق التوصيل
- تم إضافة الترجمة للنصوص المعروضة
- الكود متوافق مع البنية الحالية للمشروع
