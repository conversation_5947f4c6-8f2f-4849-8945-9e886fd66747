<?php
/**
 * Form template for public-facing pages
 *
 * @var array $form_data Form data including settings and fields
 */

// استيراد ملف الترجمات
require_once plugin_dir_path(dirname(dirname(__FILE__))) . 'includes/form-translations.php';

// Extract data from the form_data array
$form_id = $form_data['form_id'];
$product_id = $form_data['product_id'];
$settings = $form_data['settings']['form_settings'];
$fields = $form_data['settings']['fields'];

// Obtener el idioma actual
$current_language = Pexlat_Form_i18n::get_current_language();

// Prepare styles based on form settings
$button_color = $settings['button_color'];
$button_text = isset($settings['button_text']) ? $settings['button_text'] : ($current_language === 'fr' ? 'Commander maintenant' : 'اطلب الآن');
$button_border_radius = $settings['button_border_radius'] . 'px';
$fields_border_radius = $settings['fields_border_radius'] . 'px';
$card_border_radius = $settings['card_border_radius'] . 'px';
$card_bg_color = $settings['card_bg_color'];
$labels_font_weight = $settings['labels_font_weight'];

// Determine spacing class based on setting
$fields_spacing_class = 'spacing-' . $settings['fields_spacing'];

// Determine shadow class based on setting
$card_shadow_class = 'shadow-' . $settings['card_shadow'];

// Determine icons position class based on setting
$icons_position = isset($settings['icons_position']) ? $settings['icons_position'] : 'right';
$icons_position_class = 'icons-' . $icons_position;

// Get form title from form data or fallback to global settings
$form_title = '';

// إذا كان لدينا معرف النموذج، نحاول الحصول على عنوان النموذج من قاعدة البيانات
if ($form_id > 0) {
    global $wpdb;
    $form_table = $wpdb->prefix . 'pexlat_form_forms';
    $db_form = $wpdb->get_row($wpdb->prepare("SELECT title FROM {$form_table} WHERE id = %d", $form_id));

    if ($db_form && !empty($db_form->title)) {
        $form_title = $db_form->title;
    }
}

// إذا لم نجد عنوان النموذج، نستخدم القيمة الافتراضية
if (empty($form_title)) {
    $form_title = get_option('pexlat_form_title_text', 'أضف معلوماتك في الأسفل لطلب هذا المنتج');

    // ترجمة عنوان النموذج
    $form_title = form_translate($form_title);
}

$form_description = get_option('pexlat_form_description_text', '');

// ترجمة وصف النموذج
$form_description = form_translate($form_description);

// Determine if form is required for add to cart
$require_form = get_option('pexlat_form_require_fields', '1') === '1';

// دالة مساعدة لتعديل سطوع اللون (إضافة الظل للألوان) - نستخدم شرط التحقق لضمان عدم تعريفها مرتين
if (!function_exists('fe_adjust_brightness')) {
    function fe_adjust_brightness($hex, $steps) {
        // تحويل HEX إلى RGB
        $r = hexdec(substr($hex, 1, 2));
        $g = hexdec(substr($hex, 3, 2));
        $b = hexdec(substr($hex, 5, 2));

        // تعديل السطوع
        $r = max(0, min(255, $r + $steps));
        $g = max(0, min(255, $g + $steps));
        $b = max(0, min(255, $b + $steps));

        // تحويل RGB إلى HEX
        return sprintf('#%02x%02x%02x', $r, $g, $b);
    }
}

// تضمين ملف مجموعات الألوان
require_once plugin_dir_path(__FILE__) . '../../includes/color-schemes.php';

// التحقق من تفعيل النظام الموحد
$unified_theme_enabled = isset($settings['unified_theme_enabled']) && $settings['unified_theme_enabled'] === 'yes';

if ($unified_theme_enabled) {
    // تطبيق مجموعة الألوان المختارة
    $color_scheme = isset($settings['color_scheme']) ? $settings['color_scheme'] : 'blue_professional';
    $settings = fe_apply_color_scheme($settings, $color_scheme);

    // تطبيق الزوايا الموحدة
    $unified_radius = isset($settings['unified_border_radius']) ? $settings['unified_border_radius'] : '8';
    $settings['card_border_radius'] = $unified_radius;
    $settings['fields_border_radius'] = $unified_radius;
    $settings['button_border_radius'] = $unified_radius;
    $settings['shipping_border_radius'] = $unified_radius;
}

// نحصل على لون الأيقونات من الإعدادات
$icons_color = isset($settings['icons_color']) ? $settings['icons_color'] : '#2563eb';
$icons_hover_color = fe_adjust_brightness($icons_color, -10);

// تحويل اللون الأساسي إلى RGB
function hex_to_rgb($hex) {
    $hex = ltrim($hex, '#');
    if (strlen($hex) == 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    return implode(', ', array_map('hexdec', str_split($hex, 2)));
}

$primary_rgb = hex_to_rgb($icons_color);

// وضع style للحاوية حسب الإعدادات
$container_style = sprintf('
    --form-bg-color: %s;
    --form-padding: %spx;
    --form-primary-color: %s;
    --form-primary-rgb: %s;
    --form-primary-hover: %s;
    --form-text-color: %s;
    --form-input-bg: %s;
    --form-input-border: %s;
    --form-input-radius: %spx;
    --button-color: %s;
    --button-gradient-color: %s;

    /* متغيرات النظام الموحد */
    --unified-border-radius: %spx;
    --form-radius: %spx;
    --button-text-color: %s;
    --form-label-color: %s;
    --form-placeholder-color: %s;
    --form-input-focus-border: %s;

    /* متغيرات طرق الشحن */
    --shipping-methods-gap: %spx;
    --shipping-methods-padding: %spx;
    --shipping-methods-border-radius: %spx;
    /* تم إزالة حدود طرق الشحن */
    --shipping-methods-bg-color: %s;
    --shipping-methods-border-color: %s;
    --shipping-methods-title-color: %s;
    --shipping-methods-price-color: %s;
    --shipping-methods-selected-bg-color: %s;
    --shipping-methods-selected-border-color: %s;
    --shipping-methods-mobile-columns: repeat(%s, 1fr);
    --shipping-methods-radio-margin: %spx;
    --shipping-methods-details-margin: %spx;

    /* متغيرات الشريط العلوي لملخص الطلب */
    --summary-header-bg: %s;
    --summary-header-border: %s;
    --summary-title-color: %s;
    --summary-toggle-color: %s;

    /* متغيرات طرق الدفع */
    --payment-methods-bg: %s;
    --payment-methods-border: %s;
    --payment-methods-title-color: %s;
    --payment-methods-text-color: %s;
',
    esc_attr($settings['card_bg_color'] ?? '#ffffff'),
    esc_attr($settings['fields_spacing'] === 'small' ? '15' : ($settings['fields_spacing'] === 'large' ? '25' : '20')),
    esc_attr($icons_color),
    esc_attr($primary_rgb),
    esc_attr($icons_hover_color),
    esc_attr($settings['text_color'] ?? '#1e293b'),
    esc_attr($settings['fields_bg_color'] ?? '#f8fafc'),
    esc_attr($settings['fields_border_color'] ?? '#e2e8f0'),
    esc_attr($settings['fields_border_radius'] ?? '6'),
    esc_attr($settings['button_color'] ?? '#4CAF50'),
    esc_attr($settings['button_gradient_color'] ?? '#38a169'),

    // متغيرات النظام الموحد
    esc_attr($settings['unified_border_radius'] ?? '8'),
    esc_attr($settings['card_border_radius'] ?? '8'),
    esc_attr($settings['button_text_color'] ?? '#ffffff'),
    esc_attr($settings['labels_color'] ?? $settings['text_color'] ?? '#475569'),
    esc_attr($settings['placeholder_color'] ?? '#94a3b8'),
    esc_attr($settings['focus_color'] ?? $icons_color),

    // إضافة متغيرات طرق الشحن
    esc_attr($settings['shipping_gap'] ?? '4'),
    esc_attr($settings['shipping_padding'] ?? '6'),
    esc_attr($settings['shipping_border_radius'] ?? '8'),
    esc_attr($settings['shipping_border_width'] ?? '0'),
    esc_attr($settings['shipping_bg_color'] ?? '#ffffff'),
    esc_attr($settings['shipping_border_color'] ?? '#e2e8f0'),
    esc_attr($settings['shipping_title_color'] ?? '#1e293b'),
    esc_attr($settings['shipping_price_color'] ?? '#2563eb'),
    esc_attr($settings['shipping_selected_bg_color'] ?? 'rgba(37, 99, 235, 0.05)'),
    esc_attr($settings['shipping_selected_border_color'] ?? '#2563eb'),
    esc_attr($settings['shipping_mobile_columns'] ?? '1'),
    esc_attr($settings['shipping_radio_margin'] ?? '5'),
    esc_attr($settings['shipping_details_margin'] ?? '5'),

    // متغيرات الشريط العلوي لملخص الطلب
    esc_attr($settings['summary_header_bg'] ?? $settings['fields_bg_color'] ?? '#f8fafc'),
    esc_attr($settings['summary_header_border'] ?? $settings['fields_border_color'] ?? '#e2e8f0'),
    esc_attr($settings['summary_title_color'] ?? $settings['text_color'] ?? '#1e293b'),
    esc_attr($settings['summary_toggle_color'] ?? $settings['placeholder_color'] ?? '#94a3b8'),

    // متغيرات طرق الدفع
    esc_attr($settings['payment_methods_bg'] ?? $settings['card_bg_color'] ?? '#ffffff'),
    esc_attr($settings['payment_methods_border'] ?? $settings['fields_border_color'] ?? '#e2e8f0'),
    esc_attr($settings['payment_methods_title_color'] ?? $settings['text_color'] ?? '#1e293b'),
    esc_attr($settings['payment_methods_text_color'] ?? $settings['labels_color'] ?? '#64748b')
);

$border_style = sprintf('
    border: %spx %s %s;
    border-radius: %spx;
',
    esc_attr($settings['form_border_width'] ?? ($settings['card_shadow'] === 'none' ? '1' : '2')),
    esc_attr($settings['form_border_style'] ?? 'solid'),
    esc_attr($settings['form_border_color'] ?? ($settings['card_shadow'] === 'none' ? '#eaeaea' : '#e2e8f0')),
    esc_attr($settings['card_border_radius'])
);
?>
<!-- ربط مكتبة Font Awesome لاستخدام الأيقونات -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<?php
// تحديد اتجاه النموذج بناءً على اللغة الحالية
$is_ltr_form = false;

// فحص إذا كانت اللغة الحالية من اللغات LTR
if (in_array($current_language, ['fr', 'en', 'es'])) {
    $is_ltr_form = true;
}

// فحص إضافي: إذا كان إعداد اللغة في الإضافة من اللغات LTR
$plugin_language = get_option('pexlat_form_language', 'auto');
if (!$is_ltr_form && in_array($plugin_language, ['fr', 'en', 'es'])) {
    $is_ltr_form = true;
}

// فحص إضافي للكلمات المفتاحية في المحتوى
if (!$is_ltr_form) {
    $ltr_keywords = [
        // French keywords
        'Ajouter', 'Choisir', 'Finaliser', 'Nom complet', 'téléphone', 'wilaya', 'commune', 'adresse', 'Entrez', 'Prix', 'Frais', 'total', 'commande', 'spécifications', 'produit', 'Livraison', 'Quantité', 'Commander', 'Veuillez', 'Gratuit',
        // English keywords
        'Add', 'Choose', 'Complete', 'Full Name', 'Phone', 'State', 'Municipality', 'Address', 'Enter', 'Price', 'Shipping', 'Total', 'Order', 'Specifications', 'Product', 'Delivery', 'Quantity', 'Submit', 'Please', 'Free',
        // Spanish keywords
        'Añadir', 'Elegir', 'Completar', 'Nombre Completo', 'Teléfono', 'Estado', 'Municipio', 'Dirección', 'Ingresar', 'Precio', 'Envío', 'Total', 'Pedido', 'Especificaciones', 'Producto', 'Entrega', 'Cantidad', 'Enviar', 'Por favor', 'Gratis'
    ];

    // فحص العنوان والوصف
    $content_to_check = ($form_title ?? '') . ' ' . ($form_description ?? '');
    foreach ($ltr_keywords as $keyword) {
        if (stripos($content_to_check, $keyword) !== false) {
            $is_ltr_form = true;
            break;
        }
    }
}

// حل قوي: إجبار LTR إذا كانت اللغة من اللغات LTR بأي طريقة
if (!$is_ltr_form) {
    // فحص لغة ووردبريس
    $wp_locale = get_locale();
    if (strpos($wp_locale, 'fr') === 0 || strpos($wp_locale, 'en') === 0 || strpos($wp_locale, 'es') === 0) {
        $is_ltr_form = true;
    }

    // فحص WPML
    if (defined('ICL_LANGUAGE_CODE') && in_array(ICL_LANGUAGE_CODE, ['fr', 'en', 'es'])) {
        $is_ltr_form = true;
    }

    // فحص Polylang
    if (function_exists('pll_current_language') && in_array(pll_current_language(), ['fr', 'en', 'es'])) {
        $is_ltr_form = true;
    }

    // فحص URL للغات LTR
    $current_url = $_SERVER['REQUEST_URI'] ?? '';
    if (strpos($current_url, '/fr/') !== false || strpos($current_url, '/en/') !== false || strpos($current_url, '/es/') !== false ||
        strpos($current_url, '?lang=fr') !== false || strpos($current_url, '?lang=en') !== false || strpos($current_url, '?lang=es') !== false) {
        $is_ltr_form = true;
    }

    // فحص معاملات GET
    if (isset($_GET['lang']) && in_array($_GET['lang'], ['fr', 'en', 'es'])) {
        $is_ltr_form = true;
    }
}

// تصحيح مؤقت
echo '<!-- تصحيح: هل النموذج LTR؟ ' . ($is_ltr_form ? 'نعم' : 'لا') . ' -->';
echo '<!-- اللغة الحالية: ' . $current_language . ' -->';
echo '<!-- إعداد اللغة: ' . $plugin_language . ' -->';

// تطبيق LTR للغات الأجنبية (فرنسية، إنجليزية، إسبانية)

// إذا كان النموذج من اللغات LTR، طبق CSS خاص
if ($is_ltr_form) {
    echo '<style>
    /* إجبار LTR للغات الأجنبية بقوة */
    .pexlat-form-container {
        direction: ltr !important;
        text-align: left !important;
    }

    .pexlat-form-container * {
        direction: ltr !important;
        text-align: left !important;
    }';
} else {
    echo '<style>
    /* الحفاظ على RTL للعربية */
    .pexlat-form-container {
        direction: rtl !important;
        text-align: right !important;
    }

    .pexlat-form-container * {
        direction: rtl !important;
        text-align: right !important;
    }';
}

echo '
/* الحفاظ على العناصر الوسطية */
.pexlat-form-container h1,
.pexlat-form-container h2,
.pexlat-form-container h3,
.pexlat-form-container .pexlat-form-description,
.pexlat-form-container .quantity-input,
.pexlat-form-container .quantity-display,
.pexlat-form-container .quantity-controls input,
.pexlat-form-container button,
.pexlat-form-container .pexlat-form-submit,
.pexlat-form-container .whatsapp-button,
.pexlat-form-container .add-to-cart-button {
    text-align: center !important;
}

/* الأسعار على اليمين */
.pexlat-form-container .product-price-display,
.pexlat-form-container .shipping-price-display,
.pexlat-form-container .total-price-display,
.pexlat-form-container .shipping-method-price,
.pexlat-form-container .variant-price {
    text-align: right !important;
}';

// إضافة CSS خاص للغات LTR (فرنسية، إنجليزية، إسبانية)
if ($is_ltr_form) {
    echo '
/* إصلاح الأيقونات للغات LTR */
.pexlat-form-container .input-group {
    flex-direction: row !important;
}

.pexlat-form-container .input-group-text {
    order: 1 !important;
    border-radius: var(--form-input-radius, 4px) 0 0 var(--form-input-radius, 4px) !important;
}

.pexlat-form-container .input-group input,
.pexlat-form-container .input-group select {
    order: 2 !important;
    border-radius: 0 var(--form-input-radius, 4px) var(--form-input-radius, 4px) 0 !important;
}

/* إصلاح سهم القوائم المنسدلة للغات LTR */
.pexlat-form-container select {
    background-position: left 10px center !important;
    padding-left: 35px !important;
    padding-right: 10px !important;
}';
}

// إضافة CSS لطرق الدفع
echo '
/* تنسيق طرق الدفع */
.pexlat-form-payment-methods {
    margin: 20px 0;
    padding: 8px;
    background: #ffffff;
    border-radius: var(--form-radius, 8px);
    border: 1px solid var(--payment-methods-border, #e1e5e9);
}

.payment-methods-title {
    margin: 0 0 20px 0;
    font-size: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-methods-title i {
    color: var(--form-primary-color, #007cba);
}

.payment-methods-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

/* تنسيق خاص للشاشات المتوسطة */
@media (max-width: 992px) and (min-width: 769px) {
    .payment-method-content {
        padding: 10px 12px;
    }

    .payment-method-name {
        font-size: 13px;
    }

    .payment-method-desc {
        font-size: 11px;
    }
}

.payment-method-option {
    background: #fff;
    border: 2px solid var(--payment-methods-border, #e1e5e9);
    border-radius: var(--form-radius, 6px);
    transition: all 0.3s ease;
    overflow: hidden;
}

.payment-method-option:hover {
    border-color: var(--form-primary-color, #007cba);
    box-shadow: 0 2px 8px rgba(var(--form-primary-rgb, 0, 124, 186), 0.1);
}

.payment-method-label {
    display: block;
    cursor: pointer;
    margin: 0;
}

.payment-method-label input[type="radio"] {
    display: none;
}

.payment-method-content {
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.payment-method-header {
    display: flex;
    align-items: center;
    gap: 10px;
}

.payment-method-icon {
    width: 50px;
    height: 50px;
    background: rgba(var(--form-primary-rgb, 0, 124, 186), 0.1);
    border-radius: var(--form-radius, 12px);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--form-primary-color, #007cba);
    transition: all 0.3s ease;
    flex-shrink: 0;
    overflow: hidden;
}

.payment-method-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
}

.payment-method-info {
    flex: 1;
}

.payment-method-name {
    margin: 0 0 3px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
}

.payment-method-desc {
    margin: 0 0 3px 0;
    font-size: 12px;
    color: var(--payment-methods-text-color, #666);
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.payment-method-note {
    margin: 0;
    font-size: 11px;
    color: var(--form-primary-color, #007cba);
    font-style: italic;
}

.payment-method-note i {
    margin-right: 5px;
}

/* حالة الاختيار */
.payment-method-label input[type="radio"]:checked + .payment-method-content {
    background: rgba(var(--form-primary-rgb, 0, 124, 186), 0.05);
}

.payment-method-label input[type="radio"]:checked + .payment-method-content .payment-method-icon {
    background: var(--form-primary-color, #007cba);
    color: white;
    border-radius: 12px;
}

.payment-method-option:has(input[type="radio"]:checked) {
    border-color: var(--form-primary-color, #007cba);
    box-shadow: 0 0 0 1px var(--form-primary-color, #007cba);
}

/* تفاصيل إضافية */
.payment-method-details {
    margin-top: 20px;
}

.bank-details {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

.bank-details h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.bank-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    font-family: monospace;
    font-size: 14px;
    line-height: 1.6;
    color: #555;
    white-space: pre-line;
}

/* رسائل الخطأ */
#payment-method-error {
    margin-top: 10px;
    color: #dc3545;
    font-size: 14px;
    font-weight: 500;
}

/* تجاوب مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .payment-methods-container {
        grid-template-columns: 1fr 1fr;
        gap: 6px;
    }

    .payment-method-content {
        padding: 6px 8px;
    }

    .payment-method-header {
        gap: 6px;
    }

    .payment-method-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
        border-radius: 10px;
    }
    
    .payment-method-name {
        font-size: 12px;
        margin: 0;
    }
    
    .payment-method-desc {
        font-size: 10px;
        margin: 2px 0 0 0;
    }
    
    .payment-method-option {
        margin-bottom: 6px;
        border-width: 1px;
    }

    .payment-method-name {
        font-size: 13px;
    }

    .payment-method-desc {
        font-size: 11px;
    }

    .payment-method-note {
        font-size: 10px;
    }
}
';

echo '</style>';

// إضافة JavaScript للتأكد من التطبيق
if ($is_ltr_form) {
    echo '<script>
    document.addEventListener("DOMContentLoaded", function() {
        var container = document.querySelector(".pexlat-form-container");
        if (container) {
            container.style.direction = "ltr";
            container.style.textAlign = "left";

            // تطبيق على جميع العناصر الفرعية
            var allElements = container.querySelectorAll("*");
            for (var i = 0; i < allElements.length; i++) {
                allElements[i].style.direction = "ltr";
                allElements[i].style.textAlign = "left";
            }

            // الحفاظ على العناصر الوسطية
            var centerElements = container.querySelectorAll("h1, h2, h3, button, .quantity-input, .quantity-display, .quantity-controls input");
            for (var i = 0; i < centerElements.length; i++) {
                centerElements[i].style.textAlign = "center";
            }

            // الأسعار على اليمين
            var priceElements = container.querySelectorAll(".product-price-display, .shipping-price-display, .total-price-display, .shipping-method-price, .variant-price");
            for (var i = 0; i < priceElements.length; i++) {
                priceElements[i].style.textAlign = "right";
            }


        }
    });
    </script>';
}
?>

<?php
// الحصول على اللغة الحالية
$current_language = Pexlat_Form_i18n::get_current_language();
$lang_class = 'lang-' . $current_language;
?>
<div class="pexlat-form-container <?php echo esc_attr($icons_position_class . ' ' . $lang_class); ?>"
     style="<?php echo esc_attr($container_style); ?>"
     data-unified-theme="<?php echo $unified_theme_enabled ? 'true' : 'false'; ?>"
     data-color-scheme="<?php echo esc_attr($settings['color_scheme'] ?? 'blue_professional'); ?>">
    <?php
    // تحديد طبقات النموذج المستخدمة
    $form_css_class = 'pexlat-form-form';

    // تطبيق فئات CSS حسب تخطيط الحقول المحدد
    if (isset($settings['fields_layout'])) {
        // خيارات التخطيط الجديدة
        if ($settings['fields_layout'] === 'columns-2') {
            $form_css_class .= ' columns-layout all-screens-columns';
        } elseif ($settings['fields_layout'] === 'columns-2-desktop') {
            $form_css_class .= ' columns-layout desktop-only-columns';
        }
        // للتوافق مع الإعدادات القديمة
        elseif ($settings['fields_layout'] === 'horizontal') {
            $form_css_class .= ' horizontal-layout';
            // إضافة طبقة للهواتف المحمولة إن كانت مفعّلة
            if (isset($settings['mobile_fields_layout']) && $settings['mobile_fields_layout'] === 'horizontal') {
                $form_css_class .= ' small-screens-horizontal';
            }
        } elseif ($settings['fields_layout'] === 'mixed') {
            $form_css_class .= ' mixed-layout';
        }
    }

    // إضافة فئة حسب حجم هوامش الحقول
    if (isset($settings['fields_column_gap'])) {
        $form_css_class .= ' gap-' . esc_attr($settings['fields_column_gap']);
    }

    // إضافة فئة حسب ارتفاع الحقول
    if (isset($settings['fields_height'])) {
        $form_css_class .= ' field-height-' . esc_attr($settings['fields_height']);
    } else {
        $form_css_class .= ' field-height-medium'; // القيمة الافتراضية
    }

    // إضافة فئة حسب عدد أعمدة طرق الشحن
    if (isset($settings['shipping_columns'])) {
        if ($settings['shipping_columns'] == '1') {
            $form_css_class .= ' single-column-shipping';
        } else if ($settings['shipping_columns'] == '3') {
            $form_css_class .= ' three-column-shipping';
        } else if ($settings['shipping_columns'] == '4') {
            $form_css_class .= ' four-column-shipping';
        }
        // عدد عمودين هو الافتراضي فلا نحتاج لإضافة فئة خاصة
    }
    ?>
    <form id="pexlat-form-<?php echo esc_attr($form_id); ?>"
          class="<?php
              // تحديد الفئات بناءً على الإعدادات
              $show_quantity = isset($settings['show_quantity_controls']) ? $settings['show_quantity_controls'] : 'show';
              $show_icons = isset($settings['fields_icons_display']) ? $settings['fields_icons_display'] : 'show';

              $classes = array(
                  $form_css_class,
                  $show_icons === 'hide' ? 'hide-icons' : '',
                  $show_quantity === 'hide' ? 'hide-quantity' : ''
              );
              echo esc_attr(trim(implode(' ', array_filter($classes))));
          ?>"
          data-quantity-control="<?php echo esc_attr($show_quantity); ?>"
          data-quantity-position="<?php echo esc_attr($settings['quantity_position'] ?? 'center'); ?>"
           style="<?php echo esc_attr($border_style); ?>"
           data-settings='<?php echo esc_attr(wp_json_encode(array(
               'show_quantity' => $settings['show_quantity_controls'] ?? 'show',
               'fields_layout' => $settings['fields_layout'] ?? 'vertical',
               'quantity_position' => $settings['quantity_position'] ?? 'center'
           ))); ?>'
          method="post"
          data-product-id="<?php echo esc_attr($product_id); ?>"
          autocomplete="off"
          novalidate>

        <?php wp_nonce_field('pexlat_form_nonce', 'nonce'); ?>
        <input type="hidden" name="form_id" value="<?php echo esc_attr($form_id); ?>">
        <input type="hidden" name="product_id" value="<?php echo esc_attr($product_id); ?>">
        <input type="hidden" name="action" value="pexlat_form_add_to_cart">

        <!-- إضافة حقول مخفية للبيانات الإضافية -->
        <input type="hidden" name="shipping_cost" value="0">
        <input type="hidden" name="shipping_method" value="">
        <input type="hidden" name="shipping_method_name" value="">
        <?php
        $product = $product_id > 0 ? wc_get_product($product_id) : null;
        $product_price = $product ? $product->get_price() : 0;
        $is_in_stock = $product ? $product->is_in_stock() : true;
        ?>
        <input type="hidden" name="product_price" value="<?php echo esc_attr($product_price); ?>">
        <input type="hidden" name="variation_id" value="0">
        <input type="hidden" name="is_variable_product" value="<?php echo esc_attr($product && $product->is_type('variable') ? '1' : '0'); ?>">
        <input type="hidden" name="original_price" value="<?php echo esc_attr($product_price); ?>">
        <input type="hidden" name="product_in_stock" value="<?php echo esc_attr($is_in_stock ? '1' : '0'); ?>">

        <h2>
            <i class="fas fa-file-alt"></i>
            <?php echo esc_html($form_title); ?>
        </h2>

        <?php if (!empty($form_description)) : ?>
            <p class="pexlat-form-description"><?php echo esc_html($form_description); ?></p>
        <?php endif; ?>

        <!-- حقول النموذج المخصصة -->
        <?php
        // تحديد تخطيط الحقول (عمودي أو أفقي أو عمودين)
        $fields_layout = isset($settings['fields_layout']) ? esc_attr($settings['fields_layout']) : 'vertical';
        $fields_column_gap = isset($settings['fields_column_gap']) ? esc_attr($settings['fields_column_gap']) : 'medium';

        // تحديد الطبقات CSS حسب الإعدادات
        $fields_layout_class = '';

        // دعم خيارات التخطيط الجديدة
        if ($fields_layout === 'columns-2') {
            $fields_layout_class = 'layout-columns-2 gap-' . $fields_column_gap;
        }
        elseif ($fields_layout === 'columns-2-desktop') {
            $fields_layout_class = 'layout-columns-2-desktop gap-' . $fields_column_gap;
        }
        // للتوافق مع الإعدادات القديمة
        elseif ($fields_layout === 'horizontal') {
            $fields_layout_class = 'layout-horizontal gap-' . $fields_column_gap;
        }
        // إعداد تخطيط العمودين (صيغة قديمة)
        elseif ($fields_layout === 'columns') {
            $fields_layout_class = 'layout-columns-2 gap-' . $fields_column_gap;

            // تحديد إذا كان تخطيط العمودين فقط للشاشات الكبيرة
            if (isset($settings['mobile_columns_display']) && $settings['mobile_columns_display'] === 'desktop_only') {
                $fields_layout_class = 'layout-columns-2-desktop gap-' . $fields_column_gap;
            }
        }

        // إضافة طبقة لإظهار/إخفاء الأيقونات - سيتم تطبيق الفئة المناسبة مباشرة في عنصر FORM
        // ملاحظة: تنفيذها سيكون في عنصر form عند تعريفه وليس في متغير form_css_class
        ?>

        <?php
        // تحديد ترتيب العناصر من الإعدادات أو استخدام الترتيب الافتراضي
        $default_order = array('fields', 'variations', 'shipping', 'payment_methods', 'summary', 'button');

        // التحقق من وجود إعدادات ترتيب العناصر
        if (isset($settings['elements_order']) && is_array($settings['elements_order'])) {
            $elements_order = $settings['elements_order'];
        } else {
            $elements_order = $default_order;
        }

        // التأكد من أن جميع العناصر موجودة في الترتيب
        foreach ($default_order as $element_id) {
            if (!in_array($element_id, $elements_order)) {
                $elements_order[] = $element_id;
            }
        }

        // عرض العناصر بالترتيب المحدد
        foreach ($elements_order as $element_id) {
            switch ($element_id) {
                case 'variations':
                    // عرض قسم متغيرات المنتج إذا كان المنتج متغيراً
                    if ($product_id > 0) {
                        $product = wc_get_product($product_id);
                        if ($product && $product->is_type('variable')) {
                            // الحصول على سمات المنتج المتغير
                            $attributes = $product->get_variation_attributes();
                            if (!empty($attributes)) {
                                ?>
                                <div class="pexlat-form-variations">
                                    <h3 class="variations-title"><?php form_translate_echo('اختر مواصفات المنتج'); ?></h3>
                                    <div class="variations-container" style="width: 100%; box-sizing: border-box;">
                                        <?php foreach ($attributes as $attribute_name => $options) :
                                            // الحصول على اسم السمة بدون البادئة
                                            $attribute_label = wc_attribute_label(str_replace('pa_', '', $attribute_name));
                                            $attribute_id = sanitize_title($attribute_name);

                                            // تحديد نوع السمة (لون، مقاس، نص)
                                            $attribute_type = 'text'; // النوع الافتراضي

                                            // التعرف التلقائي على نوع السمة
                                            $attribute_name_lower = strtolower($attribute_name);
                                            if (strpos($attribute_name_lower, 'color') !== false ||
                                                strpos($attribute_name_lower, 'colour') !== false ||
                                                strpos($attribute_name_lower, 'لون') !== false) {
                                                $attribute_type = 'color';
                                            } elseif (strpos($attribute_name_lower, 'size') !== false ||
                                                     strpos($attribute_name_lower, 'مقاس') !== false ||
                                                     strpos($attribute_name_lower, 'حجم') !== false) {
                                                $attribute_type = 'size';
                                            }

                                            // الحصول على طريقة العرض المخصصة لهذه السمة
                                            $display_style = get_post_meta($product_id, '_attribute_display_' . $attribute_id, true) ?: 'default';

                                            // إذا كان الإعداد هو الافتراضي، نستخدم نمط العرض المناسب حسب نوع السمة
                                            if ($display_style === 'default') {
                                                if ($attribute_type === 'color') {
                                                    $display_style = 'circle';
                                                } else {
                                                    $display_style = 'square'; // الافتراضي للأنواع الأخرى
                                                }
                                            }
                                        ?>
                                            <div class="variation-attribute variation-type-<?php echo esc_attr($attribute_type); ?> variation-display-<?php echo esc_attr($display_style); ?>">
                                                <div class="variation-attribute-label">
                                                    <?php echo esc_html($attribute_label); ?>:
                                                </div>
                                                <div class="variation-buttons-container" data-attribute="<?php echo esc_attr('attribute_' . $attribute_id); ?>">
                                                    <?php foreach ($options as $option) :
                                                        $option_id = sanitize_title($option);

                                                        // البحث عن صورة المتغير من ووكومرس
                                                        $variation_image = '';
                                                        $variations = $product->get_available_variations();
                                                        foreach ($variations as $variation) {
                                                            $variation_attributes = $variation['attributes'];
                                                            if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                $variation_attributes['attribute_' . $attribute_id] === $option) {
                                                                // استخدام صورة المتغير من ووكومرس
                                                                if (!empty($variation['image']['thumb_src'])) {
                                                                    $variation_image = $variation['image']['thumb_src'];
                                                                    break;
                                                                }
                                                            }
                                                        }

                                                        // إضافة فئة للصورة المخصصة
                                                        $has_image_class = $variation_image ? 'has-image' : '';

                                                        // البحث عن معلومات المتغير (للعرض الممتد)
                                                        $variation_price = '';
                                                        $variation_regular_price = '';
                                                        $variation_description = '';
                                                        $discount_percentage = '';

                                                        // تم إزالة كود العرض الممتد
                                                    ?>
                                                        <?php if ($display_style === 'square') : ?>
                                                            <!-- نمط العرض المربع (جنبًا إلى جنب) -->
                                                            <label class="variation-button-label <?php echo esc_attr($has_image_class); ?>">
                                                                <input type="radio"
                                                                    name="attribute_<?php echo esc_attr($attribute_id); ?>"
                                                                    value="<?php echo esc_attr($option); ?>"
                                                                    class="variation-button-input"
                                                                    <?php
                                                                    // البحث عن الكمية المطلوبة للمتغير والسعر
                                                                    $variation_id = 0;
                                                                    $variation_price = 0;
                                                                    $required_quantity = '';
                                                                    foreach ($variations as $variation) {
                                                                        $variation_attributes = $variation['attributes'];
                                                                        if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                            $variation_attributes['attribute_' . $attribute_id] === $option) {
                                                                            $variation_id = $variation['variation_id'];
                                                                            $variation_price = $variation['display_price'];
                                                                            $required_quantity = get_post_meta($variation_id, '_variation_required_quantity', true);
                                                                            break;
                                                                        }
                                                                    }
                                                                    ?>
                                                                    data-variation-id="<?php echo esc_attr($variation_id); ?>"
                                                                    data-variation-price="<?php echo esc_attr($variation_price); ?>"
                                                                    <?php if (!empty($required_quantity)) : ?>
                                                                    data-required-quantity="<?php echo esc_attr($required_quantity); ?>"
                                                                    <?php endif; ?>>
                                                                <span class="variation-button" <?php if ($variation_image) echo 'style="background-image: url(' . esc_url($variation_image) . ')"'; ?>>
                                                                    <?php if (!$variation_image) echo esc_html($option); ?>
                                                                </span>
                                                                <span class="variation-name"><?php echo esc_html($option); ?></span>
                                                            </label>
                                                        <?php elseif ($display_style === 'circle' && $attribute_type === 'color') : ?>
                                                            <!-- نمط العرض الدائري للألوان -->
                                                            <label class="variation-button-label color-circle">
                                                                <input type="radio"
                                                                    name="attribute_<?php echo esc_attr($attribute_id); ?>"
                                                                    value="<?php echo esc_attr($option); ?>"
                                                                    class="variation-button-input"
                                                                    <?php
                                                                    // البحث عن الكمية المطلوبة للمتغير والسعر
                                                                    $variation_id = 0;
                                                                    $variation_price = 0;
                                                                    $required_quantity = '';
                                                                    foreach ($variations as $variation) {
                                                                        $variation_attributes = $variation['attributes'];
                                                                        if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                            $variation_attributes['attribute_' . $attribute_id] === $option) {
                                                                            $variation_id = $variation['variation_id'];
                                                                            $variation_price = $variation['display_price'];
                                                                            $required_quantity = get_post_meta($variation_id, '_variation_required_quantity', true);
                                                                            break;
                                                                        }
                                                                    }
                                                                    ?>
                                                                    data-variation-id="<?php echo esc_attr($variation_id); ?>"
                                                                    data-variation-price="<?php echo esc_attr($variation_price); ?>"
                                                                    <?php if (!empty($required_quantity)) : ?>
                                                                    data-required-quantity="<?php echo esc_attr($required_quantity); ?>"
                                                                    <?php endif; ?>>
                                                                <span class="variation-button color-circle-button">
                                                                    <span class="color-name"><?php echo esc_html($option); ?></span>
                                                                </span>
                                                            </label>
                                                        <?php elseif ($display_style === 'dropdown') : ?>
                                                            <!-- نمط العرض كقائمة منسدلة -->
                                                            <?php
                                                            // إذا كان هذا هو الخيار الأول، نقوم بإنشاء القائمة المنسدلة
                                                            if ($option === reset($options)) :
                                                            ?>
                                                                <select name="attribute_<?php echo esc_attr($attribute_id); ?>" class="variation-dropdown">
                                                                    <option value=""><?php echo esc_html("اختر {$attribute_label}"); ?></option>
                                                                    <?php foreach ($options as $dropdown_option) : ?>
                                                                        <option value="<?php echo esc_attr($dropdown_option); ?>"><?php echo esc_html($dropdown_option); ?></option>
                                                                    <?php endforeach; ?>
                                                                </select>
                                                            <?php endif; ?>
                                                        <?php elseif ($display_style === 'buttons') : ?>
                                                            <!-- نمط العرض كأزرار نصية -->
                                                            <label class="variation-button-label text-button">
                                                                <input type="radio"
                                                                    name="attribute_<?php echo esc_attr($attribute_id); ?>"
                                                                    value="<?php echo esc_attr($option); ?>"
                                                                    class="variation-button-input"
                                                                    <?php
                                                                    // البحث عن الكمية المطلوبة للمتغير
                                                                    $variation_id = 0;
                                                                    $required_quantity = '';
                                                                    foreach ($variations as $variation) {
                                                                        $variation_attributes = $variation['attributes'];
                                                                        if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                            $variation_attributes['attribute_' . $attribute_id] === $option) {
                                                                            $variation_id = $variation['variation_id'];
                                                                            $required_quantity = get_post_meta($variation_id, '_variation_required_quantity', true);
                                                                            break;
                                                                        }
                                                                    }
                                                                    if (!empty($required_quantity)) : ?>
                                                                    data-required-quantity="<?php echo esc_attr($required_quantity); ?>"
                                                                    <?php endif; ?>>
                                                                <span class="variation-button text-only-button">
                                                                    <?php echo esc_html($option); ?>
                                                                </span>
                                                            </label>
                                                        <?php elseif ($display_style === 'extended') : ?>
                                                            <!-- نمط العرض الموسع مع الاسم كاملاً -->
                                                            <?php
                                                            // البحث عن معلومات المتغير بشكل أكثر تفصيلاً
                                                            $variation_data = null;
                                                            $variation_description = '';
                                                            $variation_price = '';
                                                            $variation_regular_price = '';
                                                            $discount_percentage = '';

                                                            // البحث عن المتغير المطابق للحصول على معلوماته
                                                            foreach ($variations as $variation) {
                                                                $variation_attributes = $variation['attributes'];
                                                                if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                    $variation_attributes['attribute_' . $attribute_id] === $option) {

                                                                    // الحصول على معلومات المتغير
                                                                    $variation_data = $variation;

                                                                    // الحصول على وصف المتغير
                                                                    $variation_id = $variation['variation_id'];
                                                                    $variation_description = get_post_meta($variation_id, '_variation_description', true);

                                                                    // الحصول على سعر المتغير
                                                                    $variation_price = $variation['display_price'];
                                                                    $variation_regular_price = $variation['display_regular_price'];

                                                                    // تم إزالة حساب نسبة الخصم

                                                                    break;
                                                                }
                                                            }
                                                            ?>
                                                            <label class="variation-button-label extended-button">
                                                                <input type="radio"
                                                                    name="attribute_<?php echo esc_attr($attribute_id); ?>"
                                                                    value="<?php echo esc_attr($option); ?>"
                                                                    class="variation-button-input"
                                                                    data-variation-id="<?php echo esc_attr($variation_id); ?>"
                                                                    data-variation-price="<?php echo esc_attr($variation_price); ?>"
                                                                    <?php
                                                                    // البحث عن الكمية المطلوبة للمتغير
                                                                    $required_quantity = get_post_meta($variation_id, '_variation_required_quantity', true);
                                                                    if (!empty($required_quantity)) : ?>
                                                                    data-required-quantity="<?php echo esc_attr($required_quantity); ?>"
                                                                    <?php endif; ?>>
                                                                <div class="compact-swatch-option">
                                                                    <?php if ($variation_image) : ?>
                                                                    <div class="variant-image">
                                                                        <img src="<?php echo esc_url($variation_image); ?>" alt="<?php echo esc_attr($option); ?>">
                                                                    </div>
                                                                    <?php endif; ?>

                                                                    <div class="variant-details">
                                                                        <div class="variant-info">
                                                                            <div class="variant-title"><?php echo esc_html($option); ?></div>
                                                                            <?php if (!empty($variation_description)) : ?>
                                                                                <div class="variant-description"><?php echo esc_html($variation_description); ?></div>
                                                                            <?php endif; ?>
                                                                        </div>

                                                                        <?php if (!empty($variation_price)) : ?>
                                                                            <div class="variant-price-container">
                                                                                <div class="variant-price"><?php echo wc_price($variation_price); ?></div>
                                                                                <?php if (!empty($variation_regular_price) && $variation_regular_price > $variation_price) : ?>
                                                                                    <div class="variant-regular-price"><?php echo wc_price($variation_regular_price); ?></div>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                        <?php endif; ?>
                                                                    </div>


                                                                </div>
                                                            </label>
                                                        <?php elseif ($display_style === 'offer_cards_rectangle' || $display_style === 'offer_cards_square') : ?>
                                                            <!-- نمط العرض كبطاقات عروض (مستطيلة أو مربعة) -->
                                                            <?php
                                                            // البحث عن معلومات المتغير بشكل أكثر تفصيلاً
                                                            $variation_data = null;
                                                            $variation_description = '';
                                                            $variation_price = '';
                                                            $variation_regular_price = '';
                                                            $discount_percentage = '';
                                                            $variation_id = 0;

                                                            // البحث عن المتغير المطابق للحصول على معلوماته
                                                            foreach ($variations as $variation) {
                                                                $variation_attributes = $variation['attributes'];
                                                                if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                    $variation_attributes['attribute_' . $attribute_id] === $option) {

                                                                    // الحصول على معلومات المتغير
                                                                    $variation_data = $variation;

                                                                    // الحصول على معرف المتغير
                                                                    $variation_id = $variation['variation_id'];

                                                                    // الحصول على وصف المتغير
                                                                    $variation_description = get_post_meta($variation_id, '_variation_description', true);

                                                                    // الحصول على سعر المتغير
                                                                    $variation_price = $variation['display_price'];
                                                                    $variation_regular_price = $variation['display_regular_price'];

                                                                    // الحصول على نسبة الخصم من البيانات التعريفية أو حسابها تلقائيًا
                                                                    $discount_percentage = get_post_meta($variation_id, '_variation_discount_percentage', true);

                                                                    // إذا لم تكن نسبة الخصم محددة، قم بحسابها تلقائيًا
                                                                    if (empty($discount_percentage) && !empty($variation_regular_price) && !empty($variation_price) && $variation_regular_price > $variation_price) {
                                                                        $discount_percentage = round(100 - (($variation_price / $variation_regular_price) * 100));
                                                                    }

                                                                    // الحصول على الكمية المطلوبة من البيانات التعريفية
                                                                    $required_quantity = get_post_meta($variation_id, '_variation_required_quantity', true);

                                                                    break;
                                                                }
                                                            }

                                                            // تحديد نوع البطاقة (مستطيلة أو مربعة)
                                                            $card_class = $display_style === 'offer_cards_rectangle' ? 'rectangle-card' : 'square-card';
                                                            ?>
                                                            <label class="variation-button-label">
                                                                <input type="radio"
                                                                    name="attribute_<?php echo esc_attr($attribute_id); ?>"
                                                                    value="<?php echo esc_attr($option); ?>"
                                                                    class="variation-button-input"
                                                                    <?php if (!empty($required_quantity)) : ?>
                                                                    data-required-quantity="<?php echo esc_attr($required_quantity); ?>"
                                                                    <?php endif; ?>>
                                                                <div class="offer-card <?php echo esc_attr($card_class); ?>">
                                                                    <?php if (!empty($discount_percentage)) : ?>
                                                                        <div class="discount-badge"><?php form_translate_echo('خصم'); ?> <?php echo esc_html($discount_percentage); ?>%</div>
                                                                    <?php elseif (!empty($variation_description) && strpos($variation_description, 'عرض خاص') !== false) : ?>
                                                                        <div class="offer-special-badge"><?php form_translate_echo('عرض خاص'); ?></div>
                                                                    <?php endif; ?>

                                                                    <?php
                                                                    // التحقق من وجود كمية مطلوبة وإعداد إظهار شارة الكمية
                                                                    if (!empty($required_quantity)) :
                                                                        // البحث عن إعداد إظهار شارة الكمية
                                                                        $show_quantity_badge = 'no';
                                                                        foreach ($variations as $variation) {
                                                                            $variation_attributes = $variation['attributes'];
                                                                            if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                                $variation_attributes['attribute_' . $attribute_id] === $option) {
                                                                                $variation_id = $variation['variation_id'];
                                                                                $show_quantity_badge = get_post_meta($variation_id, '_variation_show_quantity_badge', true);
                                                                                break;
                                                                            }
                                                                        }
                                                                        // عرض شارة الكمية فقط إذا كان الإعداد "نعم"
                                                                        if ($show_quantity_badge === 'yes') :
                                                                    ?>
                                                                        <div class="quantity-badge">×<?php echo esc_html($required_quantity); ?></div>
                                                                    <?php
                                                                        endif;
                                                                    endif;
                                                                    ?>

                                                                    <?php if ($display_style === 'offer_cards_square') : ?>
                                                                        <?php if ($variation_image) : ?>
                                                                            <div class="offer-image">
                                                                                <img src="<?php echo esc_url($variation_image); ?>" alt="<?php echo esc_attr($option); ?>">
                                                                            </div>
                                                                        <?php endif; ?>

                                                                        <div class="offer-content">
                                                                            <div class="offer-title"><?php echo esc_html($option); ?></div>
                                                                            <?php if (!empty($variation_description)) : ?>
                                                                                <div class="offer-description"><?php echo esc_html($variation_description); ?></div>
                                                                            <?php endif; ?>

                                                                            <div class="offer-footer">
                                                                                <?php if (!empty($required_quantity)) : ?>
                                                                                    <div class="offer-quantity">
                                                                                        <span class="offer-quantity-label"><?php form_translate_echo('الكمية'); ?> :</span>
                                                                                        <span class="offer-quantity-value"><?php echo esc_html($required_quantity); ?></span>
                                                                                    </div>
                                                                                <?php endif; ?>

                                                                                <?php if (!empty($variation_price)) : ?>
                                                                                    <div class="offer-price">
                                                                                        <?php if (!empty($variation_regular_price) && $variation_regular_price > $variation_price) : ?>
                                                                                            <div class="price-original"><?php echo wc_price($variation_regular_price); ?></div>
                                                                                        <?php endif; ?>
                                                                                        <div class="price-current">
                                                                                            <?php
                                                                                            // استخراج السعر بدون العملة
                                                                                            $formatted_price = wc_price($variation_price);
                                                                                            // استبدال العملة بـ "دج"
                                                                                            $formatted_price = preg_replace('/<span class="woocommerce-Price-currencySymbol">.*?<\/span>/', 'دج', $formatted_price);
                                                                                            echo $formatted_price;
                                                                                            ?>
                                                                                            <span class="per-piece"><?php form_translate_echo('للقطعة'); ?></span>
                                                                                        </div>
                                                                                    </div>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                        </div>
                                                                    <?php else : ?>
                                                                        <?php if ($variation_image) : ?>
                                                                            <div class="offer-image">
                                                                                <img src="<?php echo esc_url($variation_image); ?>" alt="<?php echo esc_attr($option); ?>">
                                                                            </div>
                                                                        <?php endif; ?>

                                                                        <div class="offer-content">
                                                                            <div class="offer-title"><?php echo esc_html($option); ?></div>
                                                                            <?php if (!empty($variation_description)) : ?>
                                                                                <div class="offer-description"><?php echo esc_html($variation_description); ?></div>
                                                                            <?php endif; ?>

                                                                            <div class="offer-footer">
                                                                                <?php if (!empty($required_quantity)) : ?>
                                                                                    <div class="offer-quantity">
                                                                                        <span class="offer-quantity-label"><?php form_translate_echo('الكمية'); ?> :</span>
                                                                                        <span class="offer-quantity-value"><?php echo esc_html($required_quantity); ?></span>
                                                                                    </div>
                                                                                <?php endif; ?>

                                                                                <?php if (!empty($variation_price)) : ?>
                                                                                    <div class="offer-price">
                                                                                        <?php if (!empty($variation_regular_price) && $variation_regular_price > $variation_price) : ?>
                                                                                            <div class="price-original"><?php echo wc_price($variation_regular_price); ?></div>
                                                                                        <?php endif; ?>
                                                                                        <div class="price-current">
                                                                                            <?php
                                                                                            // استخراج السعر بدون العملة
                                                                                            $formatted_price = wc_price($variation_price);
                                                                                            // استبدال العملة بـ "دج"
                                                                                            $formatted_price = preg_replace('/<span class="woocommerce-Price-currencySymbol">.*?<\/span>/', 'دج', $formatted_price);
                                                                                            echo $formatted_price;
                                                                                            ?>
                                                                                            <span class="per-piece"><?php form_translate_echo('للقطعة'); ?></span>
                                                                                        </div>
                                                                                    </div>
                                                                                <?php endif; ?>
                                                                            </div>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </label>
                                                        <?php else : ?>
                                                            <!-- نمط العرض الافتراضي (مربع) -->
                                                            <label class="variation-button-label <?php echo esc_attr($has_image_class); ?>">
                                                                <input type="radio"
                                                                    name="attribute_<?php echo esc_attr($attribute_id); ?>"
                                                                    value="<?php echo esc_attr($option); ?>"
                                                                    class="variation-button-input"
                                                                    <?php
                                                                    // البحث عن الكمية المطلوبة للمتغير والسعر
                                                                    $variation_id = 0;
                                                                    $variation_price = 0;
                                                                    $required_quantity = '';
                                                                    foreach ($variations as $variation) {
                                                                        $variation_attributes = $variation['attributes'];
                                                                        if (isset($variation_attributes['attribute_' . $attribute_id]) &&
                                                                            $variation_attributes['attribute_' . $attribute_id] === $option) {
                                                                            $variation_id = $variation['variation_id'];
                                                                            $variation_price = $variation['display_price'];
                                                                            $required_quantity = get_post_meta($variation_id, '_variation_required_quantity', true);
                                                                            break;
                                                                        }
                                                                    }
                                                                    ?>
                                                                    data-variation-id="<?php echo esc_attr($variation_id); ?>"
                                                                    data-variation-price="<?php echo esc_attr($variation_price); ?>"
                                                                    <?php if (!empty($required_quantity)) : ?>
                                                                    data-required-quantity="<?php echo esc_attr($required_quantity); ?>"
                                                                    <?php endif; ?>>
                                                                <span class="variation-button" <?php if ($variation_image) echo 'style="background-image: url(' . esc_url($variation_image) . ')"'; ?>>
                                                                    <?php if (!$variation_image) echo esc_html($option); ?>
                                                                </span>
                                                                <span class="variation-name"><?php echo esc_html($option); ?></span>
                                                            </label>
                                                        <?php endif; ?>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <div class="variation-price-info">
                                        <span class="variation-price"></span>
                                        <span class="variation-availability"></span>
                                    </div>
                                </div>
                                <?php
                            }
                        }
                    }
                    break;

                case 'fields':
                    // عرض حقول النموذج
                    ?>
                    <div class="pexlat-form-fields <?php echo $fields_layout_class; ?>">
                    <?php foreach ($fields as $field) : ?>
                        <?php if (!$field['visible']) continue; ?>

                        <div class="form-group pexlat-form-field">
                            <?php switch ($field['type']) :
                                case 'text':
                                case 'email':
                                case 'tel':
                                case 'number':
                                    // تحديد الأيقونة المناسبة
                                    $icon_class = 'user';
                                    if ($field['type'] === 'email') $icon_class = 'envelope';
                                    if ($field['type'] === 'tel') $icon_class = 'phone-alt';
                                    if ($field['type'] === 'number') $icon_class = 'hashtag';

                                    // تحديد الاسم المناسب بناءً على معرف الحقل
                                    if (strpos($field['id'], 'name') !== false) $icon_class = 'user';
                                    if (strpos($field['id'], 'phone') !== false) $icon_class = 'phone-alt';
                                    if (strpos($field['id'], 'email') !== false) $icon_class = 'envelope';
                                    if (strpos($field['id'], 'address') !== false) $icon_class = 'map-marker-alt';
                            ?>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-<?php echo esc_attr($icon_class); ?>"></i>
                                    </span>
                                    <input
                                        type="<?php echo esc_attr($field['type']); ?>"
                                        id="<?php echo esc_attr($field['id']); ?>"
                                        name="<?php echo esc_attr($field['id']); ?>"
                                        value="<?php echo esc_attr($field['default_value']); ?>"
                                        placeholder="<?php echo esc_attr(form_translate($field['placeholder'] ?: $field['label'])); ?>"
                                        class="form-control"
                                        <?php echo $field['required'] ? 'required' : ''; ?>
                                        autocomplete="off"
                                        data-form-type="other"
                                        data-lpignore="true"
                                        readonly
                                        onfocus="this.removeAttribute('readonly');"
                                    >
                                </div>
                                <?php break; ?>

                            <?php case 'textarea': ?>
                                <label for="<?php echo esc_attr($field['id']); ?>">
                                    <?php echo esc_html(form_translate($field['label'])); ?>
                                    <?php if ($field['required']) : ?>
                                        <span class="required">*</span>
                                    <?php endif; ?>
                                </label>
                                <textarea
                                    id="<?php echo esc_attr($field['id']); ?>"
                                    name="<?php echo esc_attr($field['id']); ?>"
                                    placeholder="<?php echo esc_attr(form_translate($field['placeholder'] ?: $field['label'])); ?>"
                                    class="form-control"
                                    <?php echo $field['required'] ? 'required' : ''; ?>
                                    rows="4"
                                    autocomplete="off"
                                    data-form-type="other"
                                    data-lpignore="true"
                                ><?php echo esc_textarea($field['default_value']); ?></textarea>
                                <?php break; ?>

                            <?php case 'select':
                                // تحديد الأيقونة المناسبة
                                $icon_class = 'list';

                                // تخصيص الأيقونة بناءً على المعرف
                                if (strpos($field['id'], 'country') !== false || strpos($field['id'], 'state') !== false) $icon_class = 'map-marker-alt';
                                if (strpos($field['id'], 'municipality') !== false) $icon_class = 'city';
                            ?>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-<?php echo esc_attr($icon_class); ?>"></i>
                                    </span>
                                    <select
                                        id="<?php echo esc_attr($field['id']); ?>"
                                        name="<?php echo esc_attr($field['id']); ?>"
                                        class="form-select"
                                        <?php echo $field['required'] ? 'required' : ''; ?>
                                        autocomplete="off"
                                        data-form-type="other"
                                        data-lpignore="true"
                                    >
                                        <?php if (!empty($field['placeholder'])) : ?>
                                            <option value=""><?php echo esc_html(form_translate($field['placeholder'])); ?></option>
                                        <?php endif; ?>

                                        <?php foreach ($field['options'] as $option) : ?>
                                            <option value="<?php echo esc_attr($option); ?>" <?php selected($field['default_value'], $option); ?>>
                                                <?php echo esc_html(form_translate($option)); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php break; ?>

                            <?php case 'checkbox': ?>
                                <label>
                                    <?php echo esc_html(form_translate($field['label'])); ?>
                                    <?php if ($field['required']) : ?>
                                        <span class="required">*</span>
                                    <?php endif; ?>
                                </label>
                                <div class="checkbox-options">
                                    <?php if (!empty($field['options'])) : ?>
                                        <?php foreach ($field['options'] as $option) : ?>
                                            <label class="checkbox-label">
                                                <input
                                                    type="checkbox"
                                                    name="<?php echo esc_attr($field['id']); ?>[]"
                                                    value="<?php echo esc_attr($option); ?>"
                                                    <?php echo in_array($option, (array)$field['default_value']) ? 'checked' : ''; ?>
                                                >
                                                <?php echo esc_html(form_translate($option)); ?>
                                            </label>
                                        <?php endforeach; ?>
                                    <?php else : ?>
                                        <label class="checkbox-label">
                                            <input
                                                type="checkbox"
                                                name="<?php echo esc_attr($field['id']); ?>"
                                                value="1"
                                                <?php echo !empty($field['default_value']) ? 'checked' : ''; ?>
                                                <?php echo $field['required'] ? 'required' : ''; ?>
                                            >
                                            <?php echo !empty($field['placeholder']) ? esc_html(form_translate($field['placeholder'])) : esc_html(form_translate($field['label'])); ?>
                                        </label>
                                    <?php endif; ?>
                                </div>
                                <?php break; ?>

                            <?php case 'radio': ?>
                                <label>
                                    <?php echo esc_html(form_translate($field['label'])); ?>
                                    <?php if ($field['required']) : ?>
                                        <span class="required">*</span>
                                    <?php endif; ?>
                                </label>
                                <div class="radio-options">
                                    <?php foreach ($field['options'] as $option) : ?>
                                        <label class="radio-label">
                                            <input
                                                type="radio"
                                                name="<?php echo esc_attr($field['id']); ?>"
                                                value="<?php echo esc_attr($option); ?>"
                                                <?php echo $field['default_value'] === $option ? 'checked' : ''; ?>
                                                <?php echo $field['required'] ? 'required' : ''; ?>
                                            >
                                            <?php echo esc_html(form_translate($option)); ?>
                                        </label>
                                    <?php endforeach; ?>
                                </div>
                                <?php break; ?>

                            <?php endswitch; ?>

                            <?php if (!empty($field['description'])) : ?>
                                <p class="field-description"><?php echo esc_html(form_translate($field['description'])); ?></p>
                            <?php endif; ?>

                            <div class="field-error" id="error-<?php echo esc_attr($field['id']); ?>"></div>
                        </div>
                    <?php endforeach; ?>
                    </div><!-- نهاية pexlat-form-fields -->
                    <?php
                    break;

                case 'offers':
                    // تم إزالة قسم العروض
                    break;

                case 'shipping':
                    // عرض طرق التوصيل
                    ?>
                    <div class="pexlat-form-address-fields">
                        <!-- تم تضمين حقول الولاية والبلدية والعنوان في تكوين النموذج الرئيسي -->

                        <!-- منطقة عرض طرق التوصيل -->
                        <div class="shipping-methods-container"></div>
                    </div>
                    <?php
                    break;

                case 'payment_methods':
                    // عرض طرق الدفع من WooCommerce
                    $payment_methods_enabled = isset($settings['payment_methods_enabled']) && $settings['payment_methods_enabled'] == 1;

                    if ($payment_methods_enabled && class_exists('WooCommerce')) :
                        // الحصول على طرق الدفع المتاحة في WooCommerce
                        $all_gateways = WC()->payment_gateways->payment_gateways();
                        $available_payment_methods = array();

                        foreach ($all_gateways as $gateway_id => $gateway) {
                            // التحقق من أن الطريقة مفعلة في WooCommerce وفي النموذج
                            $wc_enabled = $gateway->enabled === 'yes';
                            $form_enabled = isset($settings['payment_gateway_' . $gateway_id]) && $settings['payment_gateway_' . $gateway_id] == 1;

                            if ($wc_enabled && $form_enabled) {
                                // تحديد الأيقونة المناسبة
                                $icon = 'fas fa-money-alt';
                                if ($gateway_id === 'cod') {
                                    $icon = 'fas fa-money-bill-wave';
                                } elseif ($gateway_id === 'bacs') {
                                    $icon = 'fas fa-university';
                                } elseif (strpos($gateway_id, 'paypal') !== false) {
                                    $icon = 'fab fa-paypal';
                                } elseif (strpos($gateway_id, 'stripe') !== false || strpos($gateway_id, 'card') !== false) {
                                    $icon = 'fas fa-credit-card';
                                }

                                $available_payment_methods[$gateway_id] = array(
                                    'id' => $gateway_id,
                                    'title' => $gateway->get_title(),
                                    'description' => $gateway->get_description(),
                                    'icon' => $icon,
                                    'has_fields' => $gateway->has_fields(),
                                    'gateway' => $gateway
                                );
                            }
                        }

                        if (!empty($available_payment_methods)) :
                    ?>
                    <div class="pexlat-form-payment-methods">
                        <h3 class="payment-methods-title">
                            <i class="fas fa-money-check-alt"></i>
                            <?php form_translate_echo('طريقة الدفع'); ?>
                        </h3>
                        <div class="payment-methods-container">
                            <?php foreach ($available_payment_methods as $method) : ?>
                                <div class="payment-method-option">
                                    <label class="payment-method-label">
                                        <input type="radio" name="payment_method" value="<?php echo esc_attr($method['id']); ?>"
                                               <?php echo count($available_payment_methods) === 1 ? 'checked' : ''; ?>
                                               data-has-fields="<?php echo $method['has_fields'] ? 'true' : 'false'; ?>">
                                        <div class="payment-method-content">
                                            <div class="payment-method-header">
                                                <div class="payment-method-icon">
                                                    <?php
                                                    // التحقق من وجود صورة شعار مخصصة
                                                    $logo_url = isset($settings['payment_logo_' . $method['id']]) ? $settings['payment_logo_' . $method['id']] : '';
                                                    if (!empty($logo_url)) : ?>
                                                        <img src="<?php echo esc_url($logo_url); ?>" alt="<?php echo esc_attr($method['title']); ?>" class="payment-method-logo">
                                                    <?php else : ?>
                                                        <i class="<?php echo esc_attr($method['icon']); ?>"></i>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="payment-method-info">
                                                    <h4 class="payment-method-name"><?php echo esc_html(form_translate($method['title'])); ?></h4>
                                                    <?php if (!empty($method['description'])) : ?>
                                                        <p class="payment-method-desc"><?php echo wp_kses_post(form_translate($method['description'])); ?></p>
                                                    <?php endif; ?>
                                                    <?php if ($method['has_fields']) : ?>
                                                        <p class="payment-method-note">
                                                            <i class="fas fa-info-circle"></i>
                                                            <?php form_translate_echo('سيتم توجيهك لإكمال تفاصيل الدفع'); ?>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="field-error" id="payment-method-error"></div>
                    </div>
                    <?php
                        endif;
                    endif;
                    break;

                case 'summary':
                    // عرض ملخص الطلب
                    $hide_summary = isset($settings['hide_order_summary']) && $settings['hide_order_summary'] == 1;
                    $collapsed_default = isset($settings['summary_collapsed_default']) && $settings['summary_collapsed_default'] == 1;

                    if ($product_id > 0 && !$hide_summary) :
                    ?>
                    <div class="total-price-container <?php echo $collapsed_default ? 'collapsed-default' : ''; ?>">
                        <div class="summary-header">
                            <h3 class="summary-title"><i class="fas fa-file-invoice"></i> <?php form_translate_echo('ملخص الطلب'); ?></h3>
                            <button type="button" class="summary-toggle-btn">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                        </div>
                        <div class="price-details">
                            <div class="price-row">
                                <span class="price-label"><i class="fas fa-tag"></i> <?php form_translate_echo('سعر المنتج:'); ?> <span class="product-quantity-badge">x<span class="quantity-number">1</span></span></span>
                                <div class="product-price-container">
                                    <?php
                                    $product = wc_get_product($product_id);
                                    if ($product) {
                                        // الحصول على طريقة عرض السعر من إعدادات المنتج
                                        $price_display_type = get_post_meta($product_id, '_price_display_type', true) ?: 'range';

                                        // التحقق مما إذا كان هناك سعر أصلي (regular_price) مختلف عن السعر الحالي
                                        $regular_price = $product->get_regular_price();
                                        $current_price = $product->get_price();

                                        // عرض السعر حسب طريقة العرض المحددة
                                        if ($price_display_type === 'min') {
                                            // عرض السعر الأدنى فقط
                                            echo '<span class="product-price-display">' . wc_price($current_price) . '</span>';
                                        } else {
                                            // عرض السعر العادي أو نطاق السعر
                                            if ($regular_price && $regular_price > $current_price) {
                                                // عرض السعر الأصلي المشطوب أولاً
                                                echo '<span class="product-regular-price">' . wc_price($regular_price) . '</span>';
                                            }

                                            // عرض السعر الحالي
                                            echo '<span class="product-price-display">' . wc_price($current_price) . '</span>';
                                        }
                                    }
                                    ?>
                                    <span class="product-quantity-display"></span>
                                </div>
                            </div>

                            <!-- طرق التوصيل المبسطة في ملخص الطلب -->
                            <?php if (($settings['shipping_display_mode'] ?? 'detailed') === 'simple'): ?>
                            <div class="price-row shipping-price-row shipping-methods-simple"
                                 data-disable-duplicate="<?php echo isset($settings['disable_duplicate_shipping_row']) ? intval($settings['disable_duplicate_shipping_row']) : 0; ?>">
                                <span class="price-label"><i class="fas fa-truck"></i> <?php form_translate_echo('سعر الشحن'); ?></span>
                                <div class="simple-shipping-methods-container"></div>
                            </div>
                            <?php endif; ?>

                            <?php if (($settings['shipping_display_mode'] ?? 'detailed') === 'detailed'): ?>
                            <div class="price-row shipping-price-row"
                                 data-disable-duplicate="<?php echo isset($settings['disable_duplicate_shipping_row']) ? intval($settings['disable_duplicate_shipping_row']) : 0; ?>">
                                <span class="price-label"><i class="fas fa-truck"></i> <?php form_translate_echo('سعر التوصيل:'); ?></span>
                                <span class="shipping-price-display">0</span>
                            </div>
                            <?php endif; ?>
                            <div class="price-row total">
                                <span class="price-label"><i class="fas fa-calculator"></i> <?php form_translate_echo('السعر الإجمالي:'); ?></span>
                                <span class="total-price-display"><?php
                                    echo $product ? wc_price($product->get_price()) : '';
                                ?></span>
                            </div>
                        </div>
                    </div>
                    <?php
                    endif;
                    break;



                case 'button':
                    // عرض الأزرار وعنصر الكمية
                    $show_quantity = $settings['show_quantity_controls'] ?? 'show';
                    $quantity_position = isset($settings['quantity_position']) ? $settings['quantity_position'] : 'center';
                    $quantity_class = $quantity_position === 'inline' ? ' inline-quantity' : '';
                    ?>
                    <div class="pexlat-form-actions<?php echo isset($settings['show_quantity_controls']) && $settings['show_quantity_controls'] === 'hide' ? ' no-quantity' : $quantity_class; ?>">

                        <?php
                        // عرض عنصر الكمية فوق زر الطلب إذا كان الموضع "في الوسط"
                        if ($show_quantity === 'show' && $quantity_position === 'center') : ?>
                        <div class="quantity-controls-container">
                            <div class="quantity-controls">
                                <button type="button" class="quantity-btn minus">-</button>
                                <input type="number" name="quantity" value="1" min="1" class="quantity-input" />
                                <button type="button" class="quantity-btn plus">+</button>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="main-action-row">
                        <?php
                        // عرض عنصر الكمية بجانب الزر إذا كان الموضع "بجانب زر الطلب"
                        if ($show_quantity === 'show' && $quantity_position === 'inline') : ?>
                            <div class="quantity-controls">
                                <button type="button" class="quantity-btn minus">-</button>
                                <input type="number" name="quantity" value="1" min="1" class="quantity-input" />
                                <button type="button" class="quantity-btn plus">+</button>
                            </div>
                        <?php endif; ?>
            <?php
            // تطبيق التدرج اللوني افتراضيًا
            $primary_color = esc_attr($settings['button_color'] ?? '#4CAF50');
            $gradient_color = esc_attr($settings['button_gradient_color'] ?? '#38a169');

            // تحديد نمط التدرج اللوني
            $gradient_enabled = isset($settings['button_gradient']) && $settings['button_gradient'] === 'yes';

            // تحديد اتجاه التدرج اللوني للزر الرئيسي
            $gradient_direction = isset($settings['button_gradient_direction']) ? esc_attr($settings['button_gradient_direction']) : 'to bottom';

            // تطبيق لون الزر حسب إعداد التدرج - نستخدم المتغيرات CSS بدلاً من التطبيق المباشر
            $button_style = $gradient_enabled
                ? "--button-color: {$primary_color}; --button-gradient-color: {$gradient_color}; --button-gradient-direction: {$gradient_direction};"
                : "background-color: {$primary_color} !important;";

            // تطبيق تأثير التحويم
            $hover_effect = isset($settings['button_hover_effect']) ? esc_attr($settings['button_hover_effect']) : 'shadow';
            $hover_class = 'hover-' . $hover_effect;

            // تطبيق تأثير الحركة
            $animation_effect = isset($settings['button_animation']) ? esc_attr($settings['button_animation']) : 'none';
            $animation_class = $animation_effect !== 'none' ? 'animation-' . $animation_effect : '';

            // تحديد أيقونة الزر وموضعها
            $has_icon = isset($settings['button_icon']) && $settings['button_icon'] !== 'none';
            $icon_position = isset($settings['button_icon_position']) ? esc_attr($settings['button_icon_position']) : 'right';
            $icon_class = $has_icon ? esc_attr($settings['button_icon']) : 'check';

            // تعيين أيقونات خاصة لكل نوع
            $icon_mapping = [
                'check' => 'check',
                'cart' => 'shopping-cart',
                'arrow' => 'arrow-right',
                'box' => 'box'
            ];
            ?>
            <?php
            // التحقق من توفر المنتج
            $is_in_stock = $product_id > 0 ? wc_get_product($product_id)->is_in_stock() : true;

            ?>
            <button
                type="submit"
                class="single_add_to_cart_button button alt pexlat-form-submit button-size-<?php echo esc_attr($settings['button_size'] ?? 'medium'); ?> <?php echo $hover_class; ?> <?php echo $gradient_enabled ? 'gradient' : ''; ?> <?php echo $animation_class; ?> <?php echo !$is_in_stock ? 'disabled' : ''; ?>"
                style="<?php echo $button_style; ?> border-color: <?php echo $primary_color; ?> !important; border-radius: <?php echo esc_attr($settings['button_border_radius']); ?>px !important; color: <?php echo esc_attr($settings['button_text_color'] ?? '#ffffff'); ?> !important;"
                <?php echo !$is_in_stock ? 'disabled' : ''; ?>
            >
            <?php if (!$is_in_stock) : ?>
                <span class="out-of-stock-message"><?php form_translate_echo('المنتج غير متوفر حالياً'); ?></span>
            <?php endif; ?>
                <?php if ($has_icon && $icon_position === 'left') : ?>
                    <span class="button-icon left"><i class="fas fa-<?php echo isset($icon_mapping[$icon_class]) ? $icon_mapping[$icon_class] : 'check'; ?>"></i></span>
                <?php endif; ?>

                <span class="button-text-content">
                    <?php echo esc_html(form_translate($button_text)); ?>
                    <?php if (isset($settings['show_total_price_in_button']) && $settings['show_total_price_in_button'] == 1) : ?>
                        <span class="button-total-price" style="margin-right: 8px; font-weight: 600;">
                            (<span class="total-price-display"><?php
                                if ($product_id > 0) {
                                    $product = wc_get_product($product_id);
                                    echo $product ? wc_price($product->get_price()) : '';
                                }
                            ?></span>)
                        </span>
                    <?php endif; ?>
                </span>

                <?php if ($has_icon && $icon_position === 'right') : ?>
                    <span class="button-icon right"><i class="fas fa-<?php echo isset($icon_mapping[$icon_class]) ? $icon_mapping[$icon_class] : 'check'; ?>"></i></span>
                <?php endif; ?>

                <?php
                // تعريف متغيرات السلة قبل الاستخدام
                $cart_system_enabled = get_option('pexlat_form_cart_system_enabled', 1);
                $show_add_to_cart = get_post_meta($product_id, '_pexlat_form_show_add_to_cart', true);
                $cart_button_style = get_option('pexlat_form_cart_button_style', 'separate');
                $cart_button_text = get_post_meta($product_id, '_pexlat_form_cart_button_text', true) ?: get_option('pexlat_form_cart_button_default_text', 'أضف إلى السلة');
                $cart_button_color = get_post_meta($product_id, '_pexlat_form_cart_button_color', true) ?: get_option('pexlat_form_cart_button_default_color', '#28a745');

                // إضافة أيقونة السلة بجانب زر الطلب إذا كان النمط "icon_only"
                if ($cart_system_enabled && $show_add_to_cart === 'yes' && $product_id > 0 && $cart_button_style === 'icon_only' && $is_in_stock) : ?>
                    <span class="cart-icon-inline"
                          style="margin-left: 8px; color: <?php echo esc_attr($cart_button_color); ?>; cursor: pointer;"
                          data-product-id="<?php echo esc_attr($product_id); ?>"
                          data-original-text="<?php echo esc_attr($cart_button_text); ?>"
                          title="<?php echo esc_attr($cart_button_text); ?>">
                        <i class="fas fa-shopping-cart"></i>
                    </span>
                <?php endif; ?>
            </button>
                        </div> <!-- إغلاق main-action-row -->

            <?php
            // إضافة زر الطلب عبر واتساب إذا كان مفعلاً
            $whatsapp_enabled = isset($settings['enable_whatsapp_button']) && $settings['enable_whatsapp_button'] == 1 ? 1 : 0;
            $whatsapp_number = isset($settings['whatsapp_number']) ? $settings['whatsapp_number'] : '';
            $whatsapp_button_text = isset($settings['whatsapp_button_text']) ? $settings['whatsapp_button_text'] : ($current_language === 'fr' ? 'Commander via WhatsApp' : 'طلب عبر واتساب');
            $whatsapp_button_color = isset($settings['whatsapp_button_color']) ? $settings['whatsapp_button_color'] : '#25D366';

            // التحقق من وجود أزرار إضافية (السلة أو الواتساب)
            $has_cart_button = ($cart_system_enabled && $show_add_to_cart === 'yes' && $product_id > 0 && $cart_button_style === 'separate' && $is_in_stock);
            $has_whatsapp_button = ($whatsapp_enabled && !empty($whatsapp_number) && $is_in_stock);

            if ($has_cart_button || $has_whatsapp_button) : ?>
            <div class="pexlat-form-additional-buttons">
                <?php if ($has_cart_button) : ?>
                <button
                    type="button"
                    class="pexlat-form-add-to-cart button-size-<?php echo esc_attr($settings['button_size'] ?? 'medium'); ?> <?php echo !$is_in_stock ? 'disabled' : ''; ?>"
                    style="background-color: <?php echo esc_attr($cart_button_color); ?>; border-color: <?php echo esc_attr($cart_button_color); ?>; color: white; border-radius: <?php echo esc_attr($settings['button_border_radius']); ?>px !important;"
                    data-product-id="<?php echo esc_attr($product_id); ?>"
                    data-original-text="<?php echo esc_attr($cart_button_text); ?>"
                    <?php echo !$is_in_stock ? 'disabled' : ''; ?>
                >
                    <span class="button-icon left"><i class="fas fa-shopping-cart"></i></span>
                    <?php if (!$is_in_stock) : ?>
                        <span class="out-of-stock-message"><?php form_translate_echo('المنتج غير متوفر حالياً'); ?></span>
                    <?php else : ?>
                        <?php echo esc_html($cart_button_text); ?>
                    <?php endif; ?>
                </button>
                <?php endif; ?>

                <?php if ($has_whatsapp_button) : ?>
                <button
                    type="button"
                    class="whatsapp-order-button button-size-<?php echo esc_attr($settings['button_size'] ?? 'medium'); ?> <?php echo $hover_class; ?>"
                    style="background-color: <?php echo esc_attr($whatsapp_button_color); ?> !important; border-color: <?php echo esc_attr($whatsapp_button_color); ?> !important; border-radius: <?php echo esc_attr($settings['button_border_radius']); ?>px !important; color: #ffffff !important;"
                    onclick="orderViaWhatsApp()"
                >
                    <span class="button-icon left"><i class="fab fa-whatsapp"></i></span>
                    <?php echo esc_html(form_translate($whatsapp_button_text)); ?>
                </button>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- رسائل النجاح والخطأ -->
            <div class="pexlat-form-messages-container">
                <div class="pexlat-form-message"></div>
                <div class="pexlat-form-loading" style="display: none;">
                    <div class="spinner"></div>
                </div>
            </div>

                    </div>
        <?php
                    break;
            }
        }
        ?>
    </form>
</div>

<?php
// تحديد ما إذا كان هذا شورت كود أم لا
$is_shortcode = isset($form_data['is_shortcode']) && $form_data['is_shortcode'];

// عرض الشريط السفلي للمنتجات أو الشورت كود
if ($product_id > 0) : ?>
<!-- Sticky Order Bar -->
<?php
// إعدادات الشريط المثبت
$show_sticky_bar = isset($settings['show_sticky_bar']) ? $settings['show_sticky_bar'] : 'yes';
$show_product_info = isset($settings['sticky_bar_show_product']) ? $settings['sticky_bar_show_product'] : 'yes';

// إعدادات الخيارات الجديدة
$always_visible = isset($settings['sticky_bar_always_visible']) ? $settings['sticky_bar_always_visible'] : 'no';
$button_submit = isset($settings['sticky_bar_button_submit']) ? $settings['sticky_bar_button_submit'] : 'no';

// للشورت كود، نتحقق من إعداد الظهور الدائم
if ($is_shortcode) {
    $shortcode_always_visible = isset($settings['sticky_bar_shortcode_always_visible']) ? $settings['sticky_bar_shortcode_always_visible'] : 'yes';
    if ($shortcode_always_visible === 'yes') {
        $always_visible = 'yes';
    }
}

// الأيقونة وإعداداتها سيتم تحديدها في قسم إعدادات الزر لاحقًا
?>

<?php if ($show_sticky_bar === 'yes') : ?>
<div id="pexlat-form-sticky-bar-<?php echo esc_attr($form_id); ?>"
     class="pexlat-form-sticky-bar"
     data-always-visible="<?php echo esc_attr($always_visible); ?>"
     data-button-submit="<?php echo esc_attr($button_submit); ?>">
    <?php if ($show_product_info === 'yes') : ?>
    <div class="pexlat-form-sticky-bar-product">
        <?php
        // Get product thumbnail
        $product = wc_get_product($product_id);
        $image_url = wp_get_attachment_image_url($product->get_image_id(), 'thumbnail');
        $product_title = $product->get_title();
        $product_price = $product->get_price_html();
        ?>

        <?php if ($image_url) : ?>
        <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_title); ?>">
        <?php endif; ?>

        <div class="pexlat-form-sticky-bar-product-info">
            <p class="pexlat-form-sticky-bar-product-title"><?php echo esc_html($product_title); ?></p>
            <p class="pexlat-form-sticky-bar-product-price"><?php echo $product_price; ?></p>
        </div>
    </div>
    <?php endif; ?>

    <?php
    // إعدادات زر الشريط المثبت المستقلة - مع التحقق المزدوج والقيم الافتراضية
    $sticky_button_color = isset($settings['sticky_bar_button_color']) && !empty($settings['sticky_bar_button_color'])
        ? esc_attr($settings['sticky_bar_button_color'])
        : '#3730a3';

    $sticky_button_text_color = isset($settings['sticky_bar_button_text_color']) && !empty($settings['sticky_bar_button_text_color'])
        ? esc_attr($settings['sticky_bar_button_text_color'])
        : '#ffffff';

    $sticky_button_border_radius = isset($settings['sticky_bar_button_border_radius']) && !empty($settings['sticky_bar_button_border_radius'])
        ? esc_attr($settings['sticky_bar_button_border_radius'])
        : '5';

    // خيارات متقدمة للزر - مع التحقق من القيم وتفضيل الإعدادات الخاصة بالشريط على الإعدادات العامة
    $sticky_button_gradient = isset($settings['sticky_bar_button_gradient']) ? $settings['sticky_bar_button_gradient'] : 'yes';

    $sticky_button_gradient_color = isset($settings['sticky_bar_button_gradient_color']) && !empty($settings['sticky_bar_button_gradient_color'])
        ? esc_attr($settings['sticky_bar_button_gradient_color'])
        : '#312e81';

    $sticky_button_gradient_direction = isset($settings['sticky_bar_button_gradient_direction']) && !empty($settings['sticky_bar_button_gradient_direction'])
        ? esc_attr($settings['sticky_bar_button_gradient_direction'])
        : 'to bottom';

    $sticky_button_animation = isset($settings['sticky_bar_button_animation']) && !empty($settings['sticky_bar_button_animation'])
        ? esc_attr($settings['sticky_bar_button_animation'])
        : 'none';

    $sticky_button_icon = isset($settings['sticky_bar_button_icon']) && !empty($settings['sticky_bar_button_icon'])
        ? esc_attr($settings['sticky_bar_button_icon'])
        : 'cart';

    $sticky_button_text = isset($settings['sticky_bar_button_text']) && !empty($settings['sticky_bar_button_text'])
        ? esc_attr($settings['sticky_bar_button_text'])
        : ($current_language === 'fr' ? 'Commander maintenant' : 'اطلب الآن');

    // إعداد أيقونة زر الشريط المثبت
    $has_icon = false;
    if ($sticky_button_icon && $sticky_button_icon !== 'none') {
        $has_icon = true;
        // تحسين تحديد فئة الأيقونة
        switch ($sticky_button_icon) {
            case 'check':
                $icon_class = 'check';
                break;
            case 'arrow':
                $icon_class = 'arrow-right';
                break;
            case 'box':
                $icon_class = 'box';
                break;
            case 'check':
            default:
                $icon_class = 'check';
                break;
        }
    }

    // تسجيل قيم الإعدادات للتصحيح (يمكن إزالة هذا الكود في الإصدار النهائي)
    error_log('إعدادات الشريط المثبت: ' .
              ' Color: ' . $sticky_button_color .
              ' Text Color: ' . $sticky_button_text_color .
              ' Border Radius: ' . $sticky_button_border_radius .
              ' Gradient: ' . $sticky_button_gradient .
              ' Animation: ' . $sticky_button_animation .
              ' Always Visible: ' . $always_visible .
              ' Button Submit: ' . $button_submit);
    ?>
    <?php
    // التحقق من توفر المنتج
    $is_in_stock = $product_id > 0 ? wc_get_product($product_id)->is_in_stock() : true;
    ?>
    <button class="pexlat-form-sticky-bar-button <?php echo $sticky_button_gradient === 'yes' ? 'gradient' : ''; ?> <?php echo $sticky_button_animation !== 'none' ? 'animation-'.$sticky_button_animation : ''; ?> <?php echo !$is_in_stock ? 'disabled' : ''; ?>"
            id="pexlat-form-sticky-order-<?php echo esc_attr($form_id); ?>"
            style="--sticky-button-color: <?php echo $sticky_button_color; ?>;
                   --sticky-button-text-color: <?php echo $sticky_button_text_color; ?>;
                   --sticky-button-gradient-color: <?php echo $sticky_button_gradient_color; ?>;
                   --sticky-button-gradient-direction: <?php echo $sticky_button_gradient_direction; ?>;
                   --sticky-button-border-radius: <?php echo $sticky_button_border_radius; ?>px;
                   color: <?php echo $sticky_button_text_color; ?> !important;
                   border-radius: <?php echo $sticky_button_border_radius; ?>px !important;
                   <?php if ($sticky_button_gradient !== 'yes') : ?>
                   background-color: <?php echo $sticky_button_color; ?> !important;
                   <?php else : ?>
                   background-image: linear-gradient(<?php echo $sticky_button_gradient_direction; ?>, <?php echo $sticky_button_color; ?>, <?php echo $sticky_button_gradient_color; ?>) !important;
                   background-size: 200% 100% !important;
                   background-position: left center !important;
                   <?php endif; ?>"
            <?php echo !$is_in_stock ? 'disabled' : ''; ?>
            >
        <?php if ($has_icon) : ?>
        <i class="fas fa-<?php echo $icon_class; ?>"></i>
        <?php endif; ?>
        <span class="sticky-button-text-content">
            <?php echo esc_html(form_translate($sticky_button_text)); ?>
            <?php if (isset($settings['show_total_price_in_button']) && $settings['show_total_price_in_button'] == 1) : ?>
                <span class="sticky-button-total-price" style="margin-right: 8px; font-weight: 600;">
                    (<span class="total-price-display"><?php
                        if ($product_id > 0) {
                            $product = wc_get_product($product_id);
                            echo $product ? wc_price($product->get_price()) : '';
                        }
                    ?></span>)
                </span>
            <?php endif; ?>
        </span>
    </button>
</div>
<?php endif; ?>

<script type="text/javascript">
    jQuery(document).ready(function($) {
        var $form = $('#pexlat-form-<?php echo esc_js($form_id); ?> form');
        var $message = $('#pexlat-form-<?php echo esc_js($form_id); ?> .pexlat-form-message');
        var $loading = $('#pexlat-form-<?php echo esc_js($form_id); ?> .pexlat-form-loading');
        var $quantityInput = $('.quantity-input');
        var $productPriceDisplay = $('.product-price-display');
        var $shippingPriceDisplay = $('.shipping-price-display');
        var $totalPriceDisplay = $('.total-price-display');

        // الحصول على السعر الأساسي للمنتج
        var basePrice = <?php
            $product = wc_get_product($product_id);
            echo $product ? $product->get_price() : 0;
        ?>;

        // الحصول على السعر الأصلي للمنتج
        var regularPrice = <?php
            $product = wc_get_product($product_id);
            echo $product && $product->get_regular_price() ? $product->get_regular_price() : 0;
        ?>;

        // الحصول على طريقة عرض السعر
        var priceDisplayType = '<?php echo get_post_meta($product_id, '_price_display_type', true) ?: 'range'; ?>';

        // دالة لتحديث الأسعار
        function updatePrices() {
            var quantity = parseInt($quantityInput.val()) || 1;
            var shippingCost = parseFloat($('input[name="shipping_cost"]').val()) || 0;

            // تحديث عرض الكمية في ملخص الطلب
            $('.quantity-number').text(quantity);

            // حساب سعر المنتج الإجمالي
            var totalProductPrice = basePrice * quantity;

            // حساب السعر الأصلي الإجمالي
            var totalRegularPrice = regularPrice * quantity;

            // تحديث العرض
            var $priceContainer = $productPriceDisplay.parent();
            var priceHtml = '';

            // عرض السعر حسب طريقة العرض المحددة
            if (priceDisplayType === 'min') {
                // عرض السعر الأدنى فقط
                priceHtml = '<span class="product-price-display">' + formatPrice(totalProductPrice) + '</span>';
            } else {
                // عرض السعر العادي أو نطاق السعر
                if (regularPrice > basePrice) {
                    // إذا كان هناك سعر أصلي أعلى من السعر الحالي، أظهر كلاهما
                    priceHtml = '<span class="product-regular-price">' + formatPrice(totalRegularPrice) + '</span>' +
                                '<span class="product-price-display">' + formatPrice(totalProductPrice) + '</span>';
                } else {
                    // إذا لم يكن هناك سعر أصلي مختلف، أظهر السعر الحالي فقط
                    priceHtml = '<span class="product-price-display">' + formatPrice(totalProductPrice) + '</span>';
                }
            }

            // تحديث حاوية السعر
            $priceContainer.html(priceHtml);

            // تحديث المرجع إلى عنصر product-price-display بعد إعادة إنشائه
            $productPriceDisplay = $priceContainer.find('.product-price-display');

            $shippingPriceDisplay.html(formatPrice(shippingCost));
            $totalPriceDisplay.html(formatPrice(totalProductPrice + shippingCost));

            // تحديث السعر في زر الطلب إذا كان مفعلاً
            var $buttonTotalPrice = $('.pexlat-form-submit .button-total-price .total-price-display');
            if ($buttonTotalPrice.length > 0) {
                $buttonTotalPrice.html(formatPrice(totalProductPrice + shippingCost));
            }

            // تحديث السعر في الزر المثبت إذا كان مفعلاً
            var $stickyButtonTotalPrice = $('.pexlat-form-sticky-bar-button .sticky-button-total-price .total-price-display');
            if ($stickyButtonTotalPrice.length > 0) {
                $stickyButtonTotalPrice.html(formatPrice(totalProductPrice + shippingCost));
            }

            // تحديث القيم المخفية - إرسال سعر الوحدة وليس السعر الإجمالي
            $('input[name="product_price"]').val(basePrice);

            // معالجة عرض صف التوصيل حسب الإعداد الجديد
            handleShippingRowDisplay(quantity);
        }

        // دالة للتحكم في عرض صف التوصيل
        function handleShippingRowDisplay(quantity) {
            var $shippingRows = $('.shipping-price-row');

            if ($shippingRows.length > 0) {
                var disableDuplicate = $shippingRows.first().data('disable-duplicate');

                // الحصول على إجمالي عدد المنتجات في السلة
                var totalCartItems = getTotalCartItems();
                var currentFormQuantity = quantity || 1;

                // حساب إجمالي المنتجات (السلة الحالية + المنتج الحالي)
                var totalItems = totalCartItems + currentFormQuantity;

                // إذا كان الإعداد مفعل وهناك أكثر من منتج واحد في المجموع
                if (disableDuplicate && totalItems > 1) {
                    // إخفاء صف التوصيل
                    $shippingRows.hide();

                    // إضافة ملاحظة توضيحية إذا لم تكن موجودة
                    if (!$('.shipping-note').length) {
                        var noteText = '<?php form_translate_echo("تكلفة التوصيل محسوبة في السعر الإجمالي"); ?>';
                        $shippingRows.first().after(
                            '<div class="shipping-note" style="font-size: 12px; color: #666; text-align: center; margin: 5px 0;">' +
                            '<i class="fas fa-info-circle"></i> ' + noteText +
                            '</div>'
                        );
                    }
                } else {
                    // إظهار صف التوصيل
                    $shippingRows.show();
                    // إزالة الملاحظة إذا كانت موجودة
                    $('.shipping-note').remove();
                }
            }
        }

        // دالة للحصول على إجمالي عدد المنتجات في السلة
        function getTotalCartItems() {
            var cartCount = 0;

            // محاولة الحصول على عدد المنتجات من عداد السلة العائمة
            var $cartCounter = $('.cart-count, .floating-cart-count, .cart-counter');
            if ($cartCounter.length > 0) {
                cartCount = parseInt($cartCounter.text()) || 0;
            }

            // إذا لم نجد العداد، نحاول الحصول عليه من customCart إذا كان متوفراً
            if (cartCount === 0 && typeof customCart !== 'undefined' && customCart.getCartCount) {
                cartCount = customCart.getCartCount();
            }

            // إذا لم نجد أي طريقة، نحاول البحث في DOM عن عناصر السلة
            if (cartCount === 0) {
                var $cartItems = $('.cart-item, .cart-product-item');
                if ($cartItems.length > 0) {
                    $cartItems.each(function() {
                        var itemQuantity = parseInt($(this).find('.quantity-input, .item-quantity').val() || $(this).find('.quantity-display').text()) || 1;
                        cartCount += itemQuantity;
                    });
                }
            }

            return cartCount;
        }

        // دالة لتنسيق السعر
        function formatPrice(price) {
            // استخدام إعدادات WooCommerce للعملة إذا كانت متوفرة من formElrakami
            if (typeof formElrakami !== 'undefined' && formElrakami.currency_symbol) {
                var symbol = formElrakami.currency_symbol;
                var position = formElrakami.currency_position || 'left';
                var decimals = parseInt(formElrakami.price_decimals) || 2;
                var decimal_sep = formElrakami.price_decimal_separator || '.';
                var thousand_sep = formElrakami.price_thousand_separator || ',';

                var formatted_price = price.toFixed(decimals);
                if (decimal_sep !== '.') {
                    formatted_price = formatted_price.replace('.', decimal_sep);
                }
                if (thousand_sep) {
                    formatted_price = formatted_price.replace(/\B(?=(\d{3})+(?!\d))/g, thousand_sep);
                }

                switch (position) {
                    case 'left':
                        return symbol + formatted_price;
                    case 'right':
                        return formatted_price + symbol;
                    case 'left_space':
                        return symbol + ' ' + formatted_price;
                    case 'right_space':
                        return formatted_price + ' ' + symbol;
                    default:
                        return symbol + formatted_price;
                }
            }

            // استخدام إعدادات WooCommerce للعملة إذا كانت متوفرة من woocommerce_params
            if (typeof woocommerce_params !== 'undefined' && woocommerce_params.currency_format_symbol) {
                var symbol = woocommerce_params.currency_format_symbol;
                var position = woocommerce_params.currency_format_symbol_pos || 'left';
                var decimals = parseInt(woocommerce_params.currency_format_num_decimals) || 2;
                var decimal_sep = woocommerce_params.currency_format_decimal_sep || '.';
                var thousand_sep = woocommerce_params.currency_format_thousand_sep || ',';

                var formatted_price = price.toFixed(decimals);
                if (decimal_sep !== '.') {
                    formatted_price = formatted_price.replace('.', decimal_sep);
                }
                if (thousand_sep) {
                    formatted_price = formatted_price.replace(/\B(?=(\d{3})+(?!\d))/g, thousand_sep);
                }

                switch (position) {
                    case 'left':
                        return symbol + formatted_price;
                    case 'right':
                        return formatted_price + symbol;
                    case 'left_space':
                        return symbol + ' ' + formatted_price;
                    case 'right_space':
                        return formatted_price + ' ' + symbol;
                    default:
                        return symbol + formatted_price;
                }
            }

            // كحل أخير، استخدم تنسيق افتراضي
            return price.toFixed(2) + ' د.ج';
        }

        // تحديث الأسعار عند تغيير الكمية
        $('.quantity-btn').on('click', function() {
            setTimeout(updatePrices, 100); // تأخير قصير للتأكد من تحديث الكمية
        });

        $quantityInput.on('change', updatePrices);

        // تحديث الأسعار عند تغيير طريقة الشحن
        $(document).on('change', 'input[name="shipping_method"]', function() {
            var shippingCost = parseFloat($(this).data('cost')) || 0;
            $('input[name="shipping_cost"]').val(shippingCost);
            updatePrices();
        });

        // تحديث بيانات طريقة الشحن عند تغيير الاختيار
        $(document).on('change', 'input[name="shipping_method_option"]', function() {
            var $this = $(this);
            var shippingCost = parseFloat($this.data('cost')) || 0;
            var shippingTitle = $this.closest('.shipping-method-option').find('.shipping-method-title').text() || 'توصيل للمنزل';

            // تحديث الحقول المخفية
            $('input[name="shipping_cost"]').val(shippingCost);
            $('input[name="shipping_method"]').val(shippingTitle);
            $('input[name="shipping_method_name"]').val(shippingTitle);

            updatePrices();
        });

        // تحديث الأسعار عند تحميل الصفحة
        updatePrices();

        // معالجة عرض صف التوصيل عند تحميل الصفحة
        var initialQuantity = parseInt($quantityInput.val()) || 1;
        handleShippingRowDisplay(initialQuantity);

        // إعادة فحص حالة السلة كل ثانية للتأكد من التحديث الفوري
        setInterval(function() {
            var currentQuantity = parseInt($quantityInput.val()) || 1;
            handleShippingRowDisplay(currentQuantity);
        }, 1000);

        // مراقبة تغييرات السلة عبر الأحداث المخصصة
        $(document).on('cart_updated cart_item_added cart_item_removed', function() {
            setTimeout(function() {
                var currentQuantity = parseInt($quantityInput.val()) || 1;
                handleShippingRowDisplay(currentQuantity);
            }, 100);
        });

        // مراقبة تغييرات عداد السلة
        var cartObserver = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    var currentQuantity = parseInt($quantityInput.val()) || 1;
                    handleShippingRowDisplay(currentQuantity);
                }
            });
        });

        // مراقبة عدادات السلة المختلفة
        $('.cart-count, .floating-cart-count, .cart-counter').each(function() {
            cartObserver.observe(this, {
                childList: true,
                characterData: true,
                subtree: true
            });
        });

        // معالجة تغيير المتغيرات
        $(document).on('change', '.variation-button-input', function() {
            var $this = $(this);
            var variationId = $this.data('variation-id') || 0;
            var variationPrice = parseFloat($this.data('variation-price')) || basePrice;

            // تحديث معرف المتغير المخفي
            $('input[name="variation_id"]').val(variationId);

            // تحديث السعر الأساسي للحسابات
            if (variationPrice > 0) {
                basePrice = variationPrice;
            }

            // تحديث الأسعار
            updatePrices();
        });

        // معالجة طرق الدفع
        $(document).on('change', 'input[name="payment_method"]', function() {
            // إزالة رسائل الخطأ
            $('#payment-method-error').empty();
        });

        // تحديد الطريقة الافتراضية إذا كانت واحدة فقط
        var $paymentMethods = $('input[name="payment_method"]');
        if ($paymentMethods.length === 1) {
            $paymentMethods.first().prop('checked', true).trigger('change');
        }

        var $stickyBar = $('#pexlat-form-sticky-bar-<?php echo esc_js($form_id); ?>');
        var $stickyOrderButton = $('#pexlat-form-sticky-order-<?php echo esc_js($form_id); ?>');
        var $formContainer = $('#pexlat-form-<?php echo esc_js($form_id); ?>');

        // ملخص الطلب - زر التبديل
        $('.summary-toggle-btn').on('click', function() {
            var $btn = $(this);
            var $details = $btn.closest('.total-price-container').find('.price-details');

            // تبديل حالة الزر والمحتوى
            $btn.toggleClass('collapsed');
            $details.toggleClass('collapsed');

            // تخزين حالة العرض في التخزين المحلي
            localStorage.setItem('summary_collapsed', $btn.hasClass('collapsed') ? 'true' : 'false');
        });

        // التحقق من الطي الافتراضي أو استعادة حالة العرض من التخزين المحلي
        var hasCollapsedDefault = $('.total-price-container').hasClass('collapsed-default');
        var isSummaryCollapsed = localStorage.getItem('summary_collapsed') === 'true';

        if (hasCollapsedDefault || isSummaryCollapsed) {
            $('.summary-toggle-btn').addClass('collapsed');
            $('.price-details').addClass('collapsed');
        }

        // Replace product form with our form on product pages
        <?php if ($require_form) : ?>
        // Hide the WooCommerce default form
        $('.single_add_to_cart_button').not('.pexlat-form-submit').parent().hide();

        // إخفاء عناصر ووكومرس المتداخلة - حل مشكلة ظهور فورم الطلب مرتين
        $('.woocommerce div.product table.variations').hide();
        $('.woocommerce div.product .variations').hide();
        $('.woocommerce div.product .single_variation_wrap').hide();
        $('.woocommerce div.product .woocommerce-variation').hide();
        $('.woocommerce div.product .variations_form').hide();
        $('.woocommerce div.product form.variations_form').hide();
        $('.woocommerce div.product form:not(.pexlat-form-form)').hide();

        $('.woocommerce div.product .woocommerce-variation-description').hide();
        $('.woocommerce div.product .woocommerce-variation-price').hide();
        $('.woocommerce div.product .woocommerce-variation-availability').hide();

        // التأكد من ظهور فورم الطلب المخصص
        $('.pexlat-form-form').show();

        // مراقبة إضافة عناصر جديدة وإخفاؤها فوراً
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            var $node = $(node);
                            // إخفاء أي عناصر ووكومرس جديدة
                            if ($node.is('table.variations, .variations, .single_variation_wrap, .woocommerce-variation, .variations_form, form.variations_form')) {
                                $node.hide();
                            }
                            // البحث داخل العقدة الجديدة
                            $node.find('table.variations, .variations, .single_variation_wrap, .woocommerce-variation, .variations_form, form.variations_form').hide();
                        }
                    });
                }
            });
        });

        // بدء مراقبة التغييرات في DOM
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        <?php endif; ?>

        // الحصول على إعدادات الشريط المثبت
        var alwaysVisible = $stickyBar.data('always-visible') === 'yes';
        var buttonSubmit = $stickyBar.data('button-submit') === 'yes';

        // إظهار أو إخفاء الشريط المثبت بناءً على رؤية النموذج
        $(window).on('scroll', function() {
            var formOffset = $formContainer.offset().top;
            var formHeight = $formContainer.outerHeight();
            var formBottom = formOffset + formHeight;
            var scrollPosition = $(window).scrollTop() + $(window).height();
            var windowScrollTop = $(window).scrollTop();
            var windowHeight = $(window).height();

            // التحقق مما إذا كان النموذج مرئيًا في الشاشة
            var isFormVisible = (
                (windowScrollTop <= formBottom && windowScrollTop + windowHeight >= formOffset) ||
                (formOffset >= windowScrollTop && formOffset <= windowScrollTop + windowHeight) ||
                (formBottom >= windowScrollTop && formBottom <= windowScrollTop + windowHeight)
            );

            // إظهار أو إخفاء الشريط بناءً على رؤية النموذج
            if (isFormVisible && !alwaysVisible) {
                // إخفاء الشريط عندما يكون النموذج مرئيًا (إلا إذا كان مضبوطًا ليكون مرئيًا دائمًا)
                $stickyBar.addClass('hidden');
            } else {
                // إظهار الشريط عندما لا يكون النموذج مرئيًا أو عندما يكون مضبوطًا ليكون مرئيًا دائمًا
                $stickyBar.removeClass('hidden');
            }
        });

        // عند النقر على زر الطلب في الشريط المثبت
        $stickyOrderButton.on('click', function() {
            if (buttonSubmit) {
                // إذا كان الزر مضبوطًا ليقوم بالطلب، قم بتنفيذ نفس وظيفة زر الطلب الرئيسي
                var $submitButton = $formContainer.find('.pexlat-form-submit');
                if ($submitButton.length > 0) {
                    $submitButton.trigger('click');
                }
            } else {
                // التمرير إلى النموذج بشكل مباشر بدون تأخير
                $('html, body').animate({
                    scrollTop: $formContainer.offset().top - 20
                }, 300, 'linear');
            }
        });

        // وظيفة الطلب عبر واتساب
        window.orderViaWhatsApp = function() {
            // التحقق من توفر رقم الواتساب
            const whatsappNumber = '<?php echo esc_js($whatsapp_number); ?>';

            if (!whatsappNumber) {
                showToast('عذراً، رقم الواتساب غير متوفر حالياً', 'error');
                return;
            }

            // مسح جميع رسائل الخطأ السابقة
            $('.field-error').empty();

            // التحقق من صحة البيانات المدخلة
            let isValid = true;
            $form.find('input[required], select[required], textarea[required]').each(function() {
                const $field = $(this);
                if (!$field.val()) {
                    isValid = false;
                    const $fieldContainer = $field.closest('.form-field');
                    const fieldLabel = $fieldContainer.find('label').text() || 'هذا الحقل';
                    $fieldContainer.find('.field-error').text(`${fieldLabel} مطلوب`);
                }
            });

            if (!isValid) {
                showToast('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من الحقول الأساسية (الاسم، الهاتف، الولاية، البلدية) إذا كانت موجودة
            const $fullNameField = $form.find('input[name="full_name"]');
            const $phoneField = $form.find('input[name="phone"]');
            const $stateField = $form.find('select[name="state"]');
            const $municipalityField = $form.find('select[name="municipality"]');

            // إعادة تعيين المتغير isValid لاستخدامه في التحقق من الحقول الأساسية
            isValid = true;
            let missingFields = [];

            // التحقق من حقل الاسم الكامل
            if ($fullNameField.length > 0 && $fullNameField.is(':visible') && (!$fullNameField.val() || $fullNameField.val().trim() === '')) {
                isValid = false;
                missingFields.push('الاسم الكامل');
                $fullNameField.addClass('error');
                const $fieldContainer = $fullNameField.closest('.form-field');
                $fieldContainer.find('.field-error').text('الاسم الكامل مطلوب');
            }

            // التحقق من حقل رقم الهاتف
            if ($phoneField.length > 0 && $phoneField.is(':visible') && (!$phoneField.val() || $phoneField.val().trim() === '')) {
                isValid = false;
                missingFields.push('رقم الهاتف');
                $phoneField.addClass('error');
                const $fieldContainer = $phoneField.closest('.form-field');
                $fieldContainer.find('.field-error').text('رقم الهاتف مطلوب');
            } else if ($phoneField.length > 0 && $phoneField.is(':visible') && $phoneField.val()) {
                // التحقق من صحة رقم الهاتف
                const phoneValue = $phoneField.val().trim();
                const digitsOnly = phoneValue.replace(/[^\d+]/g, '');

                // التحقق مما إذا كان التحقق من رقم الهاتف مفعل
                if (formElrakami.phone_validation_enabled === '1') {
                    // التحقق مما إذا كان تخصيص التحقق من رقم الهاتف مفعل
                    if (formElrakami.custom_phone_validation === '1') {
                        // الحصول على البادئات المسموح بها وطول رقم الهاتف من الإعدادات
                        const allowedPrefixes = formElrakami.phone_prefixes.split(',');
                        const phoneLength = parseInt(formElrakami.phone_length);

                        // التحقق من طول رقم الهاتف
                        if (digitsOnly.length !== phoneLength &&
                            !(digitsOnly.startsWith('+') && digitsOnly.length === phoneLength + 4)) { // +213 format
                            isValid = false;
                            $phoneField.addClass('error');
                            const $fieldContainer = $phoneField.closest('.form-field');
                            $fieldContainer.find('.field-error').text('رقم الهاتف غير صحيح. يجب أن يكون ' + phoneLength + ' أرقام');
                        }
                        // التحقق من البادئة
                        else {
                            let hasValidPrefix = false;
                            for (let i = 0; i < allowedPrefixes.length; i++) {
                                const prefix = allowedPrefixes[i].trim();
                                if (digitsOnly.startsWith(prefix) ||
                                    (digitsOnly.startsWith('+213') && digitsOnly.substring(4).startsWith(prefix.substring(1)))) {
                                    hasValidPrefix = true;
                                    break;
                                }
                            }

                            if (!hasValidPrefix) {
                                isValid = false;
                                $phoneField.addClass('error');
                                const $fieldContainer = $phoneField.closest('.form-field');
                                $fieldContainer.find('.field-error').text('رقم الهاتف غير صحيح. يجب أن يبدأ بإحدى البادئات: ' + formElrakami.phone_prefixes);
                            }
                        }
                    } else {
                        // استخدام التحقق الافتراضي
                        // Algeria phone numbers can be 10 digits (0X XX XX XX XX)
                        // or international format +213 X XX XX XX XX
                        if (!/^(\+?213|0)[5-7]\d{8}$/.test(digitsOnly)) {
                            isValid = false;
                            $phoneField.addClass('error');
                            const $fieldContainer = $phoneField.closest('.form-field');
                            $fieldContainer.find('.field-error').text('رقم الهاتف غير صحيح. يجب أن يكون رقم جزائري صالح');
                        }
                    }
                }
            }

            // التحقق من حقل الولاية
            if ($stateField.length > 0 && $stateField.is(':visible') && (!$stateField.val() || $stateField.val() === '')) {
                isValid = false;
                missingFields.push('الولاية');
                $stateField.addClass('error');
                $('#error-state').text('يرجى اختيار الولاية');
            }

            // التحقق من حقل البلدية
            if ($municipalityField.length > 0 && $municipalityField.is(':visible') && (!$municipalityField.val() || $municipalityField.val() === '')) {
                isValid = false;
                missingFields.push('البلدية');
                $municipalityField.addClass('error');
                $('#error-municipality').text('يرجى اختيار البلدية');
            }

            // التحقق من اختيار طريقة الشحن إذا كانت متوفرة ومرئية
            const $shippingMethods = $form.find('input[name="shipping_method_option"]');
            const $shippingContainer = $form.find('.shipping-methods-container');

            if ($shippingMethods.length > 0 &&
                $shippingContainer.is(':visible') &&
                !$form.find('input[name="shipping_method_option"]:checked').length) {
                isValid = false;
                missingFields.push('طريقة الشحن');
                if (!$shippingContainer.find('.field-error').length) {
                    $shippingContainer.append('<div class="field-error">يرجى اختيار طريقة التوصيل</div>');
                } else {
                    $shippingContainer.find('.field-error').text('يرجى اختيار طريقة التوصيل');
                }
            }

            // التحقق من اختيار طريقة الدفع إذا كانت متوفرة ومرئية
            const $paymentMethods = $form.find('input[name="payment_method"]');
            const $paymentContainer = $form.find('.pexlat-form-payment-methods');

            if ($paymentMethods.length > 0 &&
                $paymentContainer.is(':visible') &&
                !$form.find('input[name="payment_method"]:checked').length) {
                isValid = false;
                missingFields.push('طريقة الدفع');
                const $paymentError = $('#payment-method-error');
                if ($paymentError.length > 0) {
                    $paymentError.text('يرجى اختيار طريقة الدفع');
                } else {
                    $paymentContainer.append('<div class="field-error" id="payment-method-error">يرجى اختيار طريقة الدفع</div>');
                }
            } else {
                // إزالة رسالة الخطأ إذا تم اختيار طريقة دفع
                $('#payment-method-error').empty();
            }

            if (!isValid) {
                if (missingFields.length > 0) {
                    showToast('يرجى ملء الحقول التالية: ' + missingFields.join('، '), 'error');
                } else {
                    showToast('يرجى تصحيح الأخطاء في النموذج', 'error');
                }
                return;
            }

            // جمع بيانات النموذج
            const formData = {};
            $form.serializeArray().forEach(function(item) {
                formData[item.name] = item.value;
            });

            // الحصول على بيانات المنتج
            const productName = '<?php echo esc_js($product ? $product->get_name() : ''); ?>';
            const quantity = parseInt($quantityInput.val()) || 1;

            // الحصول على السعر من ملخص الطلب (نفس السعر المعروض للمستخدم)
            const productPriceText = $productPriceDisplay.text().trim();
            const totalPriceText = $totalPriceDisplay.text().trim();

            // تحويل النص إلى رقم (إزالة العملة والفواصل)
            const productPrice = parseFloat(productPriceText.replace(/[^\d.-]/g, '')) || (basePrice * quantity);
            const totalPrice = parseFloat(totalPriceText.replace(/[^\d.-]/g, '')) || productPrice;



            // الحصول على بيانات العميل
            let fullName = '';
            let firstName = '';
            let lastName = '';
            let phone = '';
            let address = '';
            let locationDetails = {
                country: '',
                state: '',
                stateCode: '',
                city: ''
            };
            let additionalInfo = {};

            // البحث عن حقول العميل في النموذج
            $form.find('input, select, textarea').each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                const fieldValue = $field.val();
                const fieldLabel = $field.closest('.form-field').find('label').text() || fieldName;

                if (!fieldName || !fieldValue) return;

                // تحديد نوع الحقل بناءً على الاسم
                if (fieldName === 'full_name' || fieldName.includes('name') || fieldName.includes('الاسم')) {
                    fullName = fieldValue;
                    // تقسيم الاسم الكامل إلى اسم أول واسم ثاني
                    const nameParts = fieldValue.trim().split(/\s+/);
                    firstName = nameParts[0] || '';
                    lastName = nameParts.slice(1).join(' ') || '';
                } else if (fieldName === 'phone' || fieldName.includes('phone') || fieldName.includes('هاتف') || fieldName.includes('جوال')) {
                    phone = fieldValue;
                } else if (fieldName === 'address' || fieldName.includes('address') || fieldName.includes('عنوان')) {
                    address = fieldValue;
                } else if (fieldName === 'country' || fieldName.includes('country') || fieldName.includes('دولة')) {
                    locationDetails.country = fieldValue;
                } else if (fieldName === 'state' || fieldName.includes('state') || fieldName.includes('ولاية')) {
                    locationDetails.stateCode = fieldValue;
                    // الحصول على اسم الولاية من النص المعروض في القائمة المنسدلة
                    const stateText = $field.find('option:selected').text();
                    if (stateText && stateText.includes(' - ')) {
                        locationDetails.state = stateText.split(' - ')[1];
                    } else {
                        locationDetails.state = fieldValue;
                    }
                } else if (fieldName === 'municipality' || fieldName === 'city' || fieldName.includes('city') || fieldName.includes('مدينة') || fieldName.includes('بلدية')) {
                    locationDetails.city = fieldValue;
                } else if (fieldName !== 'quantity' &&
                           fieldName !== 'product_price' &&
                           fieldName !== 'shipping_cost' &&
                           fieldName !== 'shipping_method' &&
                           !fieldName.includes('attribute_')) {
                    // حفظ المعلومات الإضافية
                    additionalInfo[fieldLabel] = fieldValue;
                }
            });

            // الحصول على معلومات المتغيرات إذا كانت موجودة
            let variationInfo = '';
            let variationData = {};

            // البحث عن متغيرات المنتج من القوائم المنسدلة
            $form.find('select[name^="attribute_"]').each(function() {
                const $field = $(this);
                const attributeName = $field.find('option:first').text().split(':')[0].trim();
                const attributeValue = $field.find('option:selected').text();

                if (attributeName && attributeValue) {
                    variationInfo += `${attributeName}: ${attributeValue}%0a`;
                    variationData[attributeName] = attributeValue;
                }
            });

            // البحث عن متغيرات المنتج من الأزرار
            $form.find('.variation-options').each(function() {
                const $variationGroup = $(this);
                const attributeName = $variationGroup.find('.variation-group-title').text().trim();
                const $selectedOption = $variationGroup.find('.variation-option.selected');

                if (attributeName && $selectedOption.length > 0) {
                    const attributeValue = $selectedOption.find('.variation-option-title').text().trim();
                    variationInfo += `${attributeName}: ${attributeValue}%0a`;
                    variationData[attributeName] = attributeValue;
                }
            });

            // تنسيق رسالة الواتساب
            let message = `*طلب جديد*%0a`;
            message += `المنتج: ${productName}%0a`;

            // إضافة معلومات المتغيرات إذا كانت موجودة
            if (variationInfo) {
                message += `المواصفات:%0a${variationInfo}`;
            }

            message += `الكمية: ${quantity}%0a`;

            // تم إزالة قسم معلومات العروض والخصومات

            message += `سعر المنتج: ${formatPrice(productPrice)}%0a`;

            // إضافة معلومات العميل مع تقسيم الاسم
            if (firstName) message += `الاسم الأول: ${firstName}%0a`;
            if (lastName) message += `الاسم الثاني: ${lastName}%0a`;
            if (phone) message += `الهاتف: ${phone}%0a`;
            if (address) message += `العنوان: ${address}%0a`;

            // إضافة تفاصيل الموقع إذا كانت متوفرة
            if (locationDetails.country) message += `الدولة: ${locationDetails.country}%0a`;
            if (locationDetails.state) message += `الولاية: ${locationDetails.state}%0a`;
            if (locationDetails.city) message += `المدينة: ${locationDetails.city}%0a`;

            // إضافة المعلومات الإضافية
            for (const [label, value] of Object.entries(additionalInfo)) {
                message += `${label}: ${value}%0a`;
            }

            // إضافة تفاصيل الشحن إذا كانت متوفرة
            const $selectedShippingMethod = $('input[name="shipping_method_option"]:checked');
            if ($selectedShippingMethod.length > 0) {
                const shippingMethodTitle = $selectedShippingMethod.closest('.shipping-method-option').find('.shipping-method-title').text();
                const shippingCost = parseFloat($selectedShippingMethod.data('cost')) || 0;

                message += `طريقة الشحن: ${shippingMethodTitle}%0a`;
                message += `تكلفة الشحن: ${formatPrice(shippingCost)}%0a`;
            }

            // إضافة تفاصيل طريقة الدفع إذا كانت متوفرة
            const $selectedPaymentMethod = $('input[name="payment_method"]:checked');
            if ($selectedPaymentMethod.length > 0) {
                const paymentMethodTitle = $selectedPaymentMethod.closest('.payment-method-option').find('.payment-method-name').text();
                message += `طريقة الدفع: ${paymentMethodTitle}%0a`;

                // إضافة ملاحظة إذا كانت الطريقة تحتاج تفاصيل إضافية
                const hasFields = $selectedPaymentMethod.data('has-fields');
                if (hasFields === 'true' || hasFields === true) {
                    message += `ملاحظة: سيتم التواصل معك لإكمال تفاصيل الدفع%0a`;
                }
            }

            // إضافة السعر الإجمالي من ملخص الطلب
            message += `المبلغ الإجمالي: ${formatPrice(totalPrice)}%0a`;

            // تنسيق رقم الواتساب (إزالة أي أحرف غير رقمية باستثناء +)
            let formattedNumber = whatsappNumber.replace(/[^\d+]/g, '');

            // إذا كان الرقم يبدأ بـ 0، نستبدله بـ 213
            if (formattedNumber.startsWith('0')) {
                formattedNumber = '213' + formattedNumber.substring(1);
            }

            // إذا كان الرقم لا يبدأ بـ +، نضيف +
            if (!formattedNumber.startsWith('+')) {
                formattedNumber = '+' + formattedNumber;
            }



            // فتح واتساب مع الرسالة
            const whatsappUrl = `https://wa.me/${formattedNumber}?text=${message}`;
            window.open(whatsappUrl, '_blank');

            // عرض رسالة نجاح
            showToast('تم فتح واتساب لإرسال الطلب', 'success');
        };



        // دالة لعرض رسائل التنبيه
        function showToast(message, type) {
            // إنشاء عنصر التنبيه إذا لم يكن موجوداً
            let $toast = $('.pexlat-form-toast');
            if ($toast.length === 0) {
                $toast = $('<div class="pexlat-form-toast"></div>');
                $('body').append($toast);
            }

            // تعيين نوع ومحتوى التنبيه
            $toast.attr('class', 'pexlat-form-toast ' + (type || 'info'));
            $toast.text(message);
            $toast.fadeIn();

            // إخفاء التنبيه بعد 3 ثوانٍ
            setTimeout(function() {
                $toast.fadeOut();
            }, 3000);
        }

        // Form submission
        $form.on('submit', function(e) {
            e.preventDefault();

            // Clear previous errors
            $('.field-error').empty();
            $message.empty().removeClass('error success');

            // Show loading spinner
            $loading.show();

            // تحديث الأسعار قبل الإرسال للتأكد من صحة البيانات
            updatePrices();

            // Collect form data
            var formData = $(this).serialize();

            // AJAX request
            $.ajax({
                url: formElrakami.ajaxurl,
                type: 'POST',
                data: formData,
                dataType: 'json',
                timeout: 30000, // زيادة مهلة الانتظار إلى 30 ثانية
                success: function(response) {
                    $loading.hide();

                    if (response.success) {
                        var successMessage = response.data.message || 'تم إرسال طلبك بنجاح.';

                        // ترجمة رسالة النجاح إذا كانت متوفرة
                        if (typeof formElrakami !== 'undefined' && formElrakami.translations) {
                            if (successMessage === 'تم إرسال طلبك بنجاح.' && formElrakami.translations['تم إرسال طلبك بنجاح.']) {
                                successMessage = formElrakami.translations['تم إرسال طلبك بنجاح.'];
                            }
                            // إذا كانت الرسالة تحتوي على النص العربي، استبدله بالترجمة
                            else if (successMessage.indexOf('تم إرسال طلبك بنجاح.') !== -1 && formElrakami.translations['تم إرسال طلبك بنجاح.']) {
                                successMessage = successMessage.replace('تم إرسال طلبك بنجاح.', formElrakami.translations['تم إرسال طلبك بنجاح.']);
                            }
                        }

                        $message.addClass('success').html(successMessage);

                        // التعامل مع طرق الدفع المختلفة
                        if (response.data.needs_payment === true) {
                            // تسجيل معلومات التشخيص
                            console.log('Payment redirect info:', {
                                order_id: response.data.order_id,
                                redirect_url: response.data.redirect,
                                payment_method: response.data.payment_method,
                                order_status: response.data.order_status,
                                order_total: response.data.order_total,
                                item_count: response.data.item_count
                            });

                            // إذا كان الطلب يحتاج دفع، نوجه فوراً إلى صفحة الدفع
                            if (response.data.redirect) {
                                setTimeout(function() {
                                    console.log('Redirecting to payment page:', response.data.redirect);
                                    window.location.href = response.data.redirect;
                                }, 1500); // وقت أطول قليلاً لقراءة الرسالة
                            } else {
                                console.error('No redirect URL provided for payment');
                            }
                        } else {
                            // السلوك العادي للدفع عند الاستلام أو عدم تفعيل طرق الدفع
                            if (response.data.redirect) {
                                setTimeout(function() {
                                    window.location.href = response.data.redirect;
                                }, 1000);
                            }
                        }
                    } else {
                        $message.addClass('error').html(response.data);

                        // If there are field-specific errors
                        if (typeof response.data === 'object' && response.data.field_errors) {
                            $.each(response.data.field_errors, function(field, error) {
                                $('#error-' + field).html(error);
                            });
                        }
                    }
                },
                error: function(xhr, status, error) {
                    $loading.hide();
                    console.error('خطأ في الاتصال:', status, error);

                    // رسالة خطأ مخصصة حسب نوع الخطأ
                    var errorMessage = 'حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';

                    if (status === 'timeout') {
                        errorMessage = 'استغرق الطلب وقتًا طويلاً. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
                    } else if (status === 'parsererror') {
                        errorMessage = 'حدث خطأ أثناء معالجة الاستجابة. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
                    } else if (status === 'abort') {
                        errorMessage = 'تم إلغاء الطلب. يرجى المحاولة مرة أخرى.';
                    } else if (xhr.status === 0) {
                        errorMessage = 'لا يمكن الاتصال بالخادم. يرجى التحقق من اتصالك بالإنترنت.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'الصفحة المطلوبة غير موجودة. يرجى تحديث الصفحة والمحاولة مرة أخرى.';
                    } else if (xhr.status === 500) {
                        errorMessage = 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقًا.';
                    }

                    // محاولة تحليل الاستجابة إذا كانت JSON
                    try {
                        var jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse && jsonResponse.data) {
                            errorMessage = jsonResponse.data;
                        }
                    } catch (e) {
                        console.error('خطأ في تحليل استجابة الخادم:', e);
                    }

                    $message.addClass('error').html(errorMessage);
                }
            });
        });

        // ===== تطبيق اتجاه النصوص حسب اللغة =====

        // الحصول على اللغة الحالية من البيانات المرسلة من PHP
        var currentLanguage = '<?php echo esc_js($current_language); ?>';
        var $formContainer = $('.pexlat-form-container');

        // التأكد من وجود كلاس اللغة الصحيح
        if (currentLanguage && currentLanguage !== '') {
            // إزالة جميع كلاسات اللغة الموجودة
            $formContainer.removeClass('lang-ar lang-fr lang-en');

            // إضافة كلاس اللغة الحالية
            $formContainer.addClass('lang-' + currentLanguage);
        }

        // حل إضافي: فحص المحتوى الديناميكي للنموذج
        function detectLanguageFromContent() {
            var formText = $formContainer.text();
            var frenchWords = ['Ajouter', 'Choisir', 'Finaliser', 'Nom complet', 'téléphone', 'wilaya', 'commune', 'adresse', 'Entrez', 'Prix', 'Frais', 'total', 'commande', 'spécifications', 'produit'];

            for (var i = 0; i < frenchWords.length; i++) {
                if (formText.indexOf(frenchWords[i]) !== -1) {
                    $formContainer.removeClass('lang-ar lang-fr lang-en');
                    $formContainer.addClass('lang-fr');

                    return 'fr';
                }
            }
            return currentLanguage;
        }

        // تطبيق الكشف عن اللغة
        var detectedLanguage = detectLanguageFromContent();

    });
</script>
<?php endif; ?>